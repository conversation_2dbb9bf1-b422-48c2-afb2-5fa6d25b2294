import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faRocket, 
  faPlay, 
  faStop,
  faBolt,
  faCoins
} from '@fortawesome/free-solid-svg-icons';
import ApiService from '../../services/ApiService';
import './CrashGame.css';

const CrashGame = ({ user, balance, updateBalance }) => {
  const [betAmount, setBetAmount] = useState('');
  const [targetMultiplier, setTargetMultiplier] = useState('2.00');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentMultiplier, setCurrentMultiplier] = useState(1.00);
  const [gameResult, setGameResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [gameHistory, setGameHistory] = useState([]);
  const canvasRef = useRef(null);
  const animationRef = useRef(null);

  useEffect(() => {
    loadGameHistory();
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const loadGameHistory = async () => {
    try {
      // In real implementation, this would fetch recent crash games
      setGameHistory([
        { multiplier: 2.45, crashed: true },
        { multiplier: 1.23, crashed: true },
        { multiplier: 5.67, crashed: true },
        { multiplier: 1.89, crashed: true },
        { multiplier: 3.21, crashed: true },
      ]);
    } catch (error) {
      console.error('Error loading game history:', error);
    }
  };

  const startGame = async () => {
    if (!betAmount || parseFloat(betAmount) <= 0) {
      alert('Введите корректную сумму ставки');
      return;
    }

    if (parseFloat(betAmount) > balance) {
      alert('Недостаточно средств');
      return;
    }

    if (!targetMultiplier || parseFloat(targetMultiplier) < 1.01) {
      alert('Минимальный множитель: 1.01');
      return;
    }

    setLoading(true);
    setGameResult(null);

    try {
      const result = await ApiService.playCrash(
        user.telegram_id,
        parseFloat(betAmount),
        parseFloat(targetMultiplier)
      );

      if (result.success) {
        setIsPlaying(true);
        animateGame(result.crash_point, parseFloat(targetMultiplier), result.payout);
        
        // Update balance
        const newBalance = balance - parseFloat(betAmount) + (result.payout || 0);
        updateBalance(newBalance);
      } else {
        alert('Ошибка при размещении ставки');
      }
    } catch (error) {
      console.error('Error starting game:', error);
      alert('Ошибка соединения с сервером');
    } finally {
      setLoading(false);
    }
  };

  const animateGame = (crashPoint, target, payout) => {
    const startTime = Date.now();
    const duration = Math.min(crashPoint * 1000, 10000); // Max 10 seconds
    
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Exponential growth curve
      const multiplier = 1 + (crashPoint - 1) * Math.pow(progress, 0.5);
      setCurrentMultiplier(multiplier);
      
      drawGraph(multiplier, crashPoint, target);
      
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        // Game ended
        setIsPlaying(false);
        setGameResult({
          crashPoint,
          target,
          payout,
          won: target <= crashPoint,
        });
        
        // Add to history
        setGameHistory(prev => [
          { multiplier: crashPoint, crashed: true },
          ...prev.slice(0, 4)
        ]);
      }
    };
    
    animate();
  };

  const drawGraph = (current, crashPoint, target) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.fillRect(0, 0, width, height);
    
    // Draw grid
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    for (let i = 0; i <= 10; i++) {
      const x = (width / 10) * i;
      const y = (height / 10) * i;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
    
    // Draw multiplier line
    const progress = (current - 1) / (Math.max(crashPoint, current) - 1);
    const x = width * Math.min(progress, 1);
    const y = height - (height * (current - 1) / (Math.max(crashPoint, current) - 1));
    
    ctx.strokeStyle = current >= crashPoint ? '#ff4757' : '#00ff88';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(0, height);
    ctx.lineTo(x, y);
    ctx.stroke();
    
    // Draw target line
    if (target > 1) {
      const targetY = height - (height * (target - 1) / (Math.max(crashPoint, current) - 1));
      ctx.strokeStyle = 'rgba(255, 215, 0, 0.8)';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.moveTo(0, targetY);
      ctx.lineTo(width, targetY);
      ctx.stroke();
      ctx.setLineDash([]);
    }
  };

  const quickBetAmounts = ['0.1', '0.5', '1.0', '2.0', '5.0'];
  const quickMultipliers = ['1.5', '2.0', '3.0', '5.0', '10.0'];

  return (
    <div className="crash-game">
      {/* Game Display */}
      <motion.div
        className="game-display glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="game-header">
          <h2>
            <FontAwesomeIcon icon={faRocket} className="text-green" />
            Crash Game
          </h2>
          <div className="current-multiplier">
            <span className={`multiplier ${isPlaying ? 'playing' : ''}`}>
              {currentMultiplier.toFixed(2)}x
            </span>
          </div>
        </div>

        <div className="game-canvas-container">
          <canvas
            ref={canvasRef}
            width={300}
            height={200}
            className="game-canvas"
          />
          {!isPlaying && !gameResult && (
            <div className="canvas-overlay">
              <FontAwesomeIcon icon={faRocket} size="3x" className="text-green" />
              <p>Готов к запуску!</p>
            </div>
          )}
        </div>

        {gameResult && (
          <motion.div
            className={`game-result ${gameResult.won ? 'won' : 'lost'}`}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="result-icon">
              <FontAwesomeIcon
                icon={gameResult.won ? faBolt : faStop}
                className={gameResult.won ? 'text-success' : 'text-danger'}
              />
            </div>
            <div className="result-text">
              {gameResult.won ? 'Победа!' : 'Крах!'}
            </div>
            <div className="result-details">
              <p>Крах на: {gameResult.crashPoint.toFixed(2)}x</p>
              <p>Ваша цель: {gameResult.target.toFixed(2)}x</p>
              {gameResult.won && (
                <p className="payout text-green">
                  Выигрыш: +{gameResult.payout.toFixed(2)} TON
                </p>
              )}
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Game Controls */}
      <motion.div
        className="game-controls glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="control-group">
          <label>Сумма ставки (TON)</label>
          <div className="input-group">
            <input
              type="number"
              value={betAmount}
              onChange={(e) => setBetAmount(e.target.value)}
              placeholder="0.00"
              className="input"
              disabled={isPlaying || loading}
              step="0.01"
              min="0.01"
            />
            <FontAwesomeIcon icon={faCoins} className="input-icon text-green" />
          </div>
          <div className="quick-buttons">
            {quickBetAmounts.map(amount => (
              <button
                key={amount}
                className="quick-btn"
                onClick={() => setBetAmount(amount)}
                disabled={isPlaying || loading}
              >
                {amount}
              </button>
            ))}
          </div>
        </div>

        <div className="control-group">
          <label>Цель (множитель)</label>
          <div className="input-group">
            <input
              type="number"
              value={targetMultiplier}
              onChange={(e) => setTargetMultiplier(e.target.value)}
              placeholder="2.00"
              className="input"
              disabled={isPlaying || loading}
              step="0.01"
              min="1.01"
            />
            <span className="input-icon">x</span>
          </div>
          <div className="quick-buttons">
            {quickMultipliers.map(mult => (
              <button
                key={mult}
                className="quick-btn"
                onClick={() => setTargetMultiplier(mult)}
                disabled={isPlaying || loading}
              >
                {mult}x
              </button>
            ))}
          </div>
        </div>

        <button
          className={`play-button btn ${isPlaying ? 'btn-danger' : 'btn-primary'}`}
          onClick={startGame}
          disabled={loading}
        >
          <FontAwesomeIcon icon={isPlaying ? faStop : faPlay} />
          {loading ? 'Загрузка...' : isPlaying ? 'Играем...' : 'Играть'}
        </button>
      </motion.div>

      {/* Game History */}
      <motion.div
        className="game-history glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h3>
          <FontAwesomeIcon icon={faBolt} className="text-gold" />
          Последние игры
        </h3>
        <div className="history-list">
          {gameHistory.map((game, index) => (
            <div
              key={index}
              className={`history-item ${game.multiplier >= 2 ? 'high' : 'low'}`}
            >
              {game.multiplier.toFixed(2)}x
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default CrashGame;
