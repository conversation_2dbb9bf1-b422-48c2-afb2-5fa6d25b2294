"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PriceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const price_entity_1 = require("./price.entity");
const XLSX = require("xlsx");
let PriceService = class PriceService {
    priceRepository;
    constructor(priceRepository) {
        this.priceRepository = priceRepository;
    }
    async getAllPrices() {
        return this.priceRepository.find();
    }
    async getPriceByModelKey(model_key) {
        return this.priceRepository.findOne({ where: { model_key } });
    }
    async updatePrice(model_key, min_ton, image_url) {
        let price = await this.priceRepository.findOne({ where: { model_key } });
        if (price) {
            price.min_ton = min_ton;
            price.image_url = image_url;
        }
        else {
            price = this.priceRepository.create({
                model_key,
                min_ton,
                image_url,
            });
        }
        return this.priceRepository.save(price);
    }
    async importFromExcel(buffer) {
        const errors = [];
        let imported = 0;
        try {
            const workbook = XLSX.read(buffer, { type: 'buffer' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const data = XLSX.utils.sheet_to_json(worksheet);
            for (const row of data) {
                try {
                    const rowData = row;
                    const model_key = rowData['model_key'];
                    const min_ton = parseFloat(rowData['min_ton']);
                    const image_url = rowData['image_url'];
                    if (!model_key || isNaN(min_ton) || !image_url) {
                        errors.push(`Invalid row: ${JSON.stringify(row)}`);
                        continue;
                    }
                    await this.updatePrice(model_key, min_ton, image_url);
                    imported++;
                }
                catch (error) {
                    errors.push(`Error processing row ${JSON.stringify(row)}: ${error.message}`);
                }
            }
            return { success: true, imported, errors };
        }
        catch (error) {
            return { success: false, imported: 0, errors: [error.message] };
        }
    }
    async deletePrice(model_key) {
        const result = await this.priceRepository.delete({ model_key });
        return (result.affected || 0) > 0;
    }
    async clearAllPrices() {
        await this.priceRepository.clear();
    }
};
exports.PriceService = PriceService;
exports.PriceService = PriceService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(price_entity_1.Price)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PriceService);
//# sourceMappingURL=price.service.js.map