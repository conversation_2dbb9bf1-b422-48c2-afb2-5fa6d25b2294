const API_BASE_URL = import.meta.env.PROD ? '' : '/api';

class ApiService {
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // User endpoints
  async createUser(userData) {
    return this.request('/users/create', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getUser(telegramId) {
    try {
      return await this.request(`/users/${telegramId}`);
    } catch (error) {
      if (error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  async updateBalance(telegramId, amount) {
    return this.request('/users/update-balance', {
      method: 'POST',
      body: JSON.stringify({
        telegram_id: telegramId,
        amount: amount,
      }),
    });
  }

  async deductBalance(telegramId, amount) {
    return this.request('/users/deduct-balance', {
      method: 'POST',
      body: JSON.stringify({
        telegram_id: telegramId,
        amount: amount,
      }),
    });
  }

  async getLeaderboard() {
    return this.request('/users/leaderboard');
  }

  async updateWallet(telegramId, walletAddress) {
    return this.request('/users/update-wallet', {
      method: 'POST',
      body: JSON.stringify({
        telegram_id: telegramId,
        wallet_address: walletAddress,
      }),
    });
  }

  // Game endpoints
  async playCrash(userId, betAmount, targetMultiplier) {
    return this.request('/games/crash/play', {
      method: 'POST',
      body: JSON.stringify({
        user_id: userId,
        bet_amount: betAmount,
        target_multiplier: targetMultiplier,
      }),
    });
  }

  async playDouble(userId, betAmount, chosenMultiplier) {
    return this.request('/games/double/play', {
      method: 'POST',
      body: JSON.stringify({
        user_id: userId,
        bet_amount: betAmount,
        chosen_multiplier: chosenMultiplier,
      }),
    });
  }

  async createCoinflipRoom(userId, stake) {
    return this.request('/games/coinflip/create', {
      method: 'POST',
      body: JSON.stringify({
        user_id: userId,
        stake: stake,
      }),
    });
  }

  async joinCoinflipRoom(roomId, userId) {
    return this.request('/games/coinflip/join', {
      method: 'POST',
      body: JSON.stringify({
        room_id: roomId,
        user_id: userId,
      }),
    });
  }

  async getWaitingRooms() {
    return this.request('/games/coinflip/waiting');
  }

  async getRecentGames() {
    return this.request('/games/coinflip/recent');
  }

  async getUserRooms(userId) {
    return this.request(`/games/coinflip/user/${userId}`);
  }

  // Transaction endpoints
  async getUserTransactions(userId, limit = 50) {
    return this.request(`/transactions/user/${userId}?limit=${limit}`);
  }

  async getUserGameStats(userId) {
    return this.request(`/transactions/stats/${userId}`);
  }

  // Price endpoints
  async getAllPrices() {
    return this.request('/prices');
  }

  async getPrice(modelKey) {
    return this.request(`/prices/${modelKey}`);
  }

  // Config endpoints
  async getWinModifier() {
    return this.request('/config/win-modifier');
  }

  async setWinModifier(modifier) {
    return this.request('/config/win-modifier', {
      method: 'POST',
      body: JSON.stringify({ modifier }),
    });
  }
}

export default new ApiService();
