import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CoinflipRoom, CoinflipStatus } from './coinflip-room.entity';
import { UserService } from '../users/user.service';
import { TransactionService } from '../transactions/transaction.service';
import { TransactionKind } from '../transactions/transaction.entity';
import * as crypto from 'crypto';

@Injectable()
export class CoinflipService {
  constructor(
    @InjectRepository(CoinflipRoom)
    private coinflipRepository: Repository<CoinflipRoom>,
    private userService: UserService,
    private transactionService: TransactionService,
  ) {}

  async createRoom(playerA_id: string, stake: number): Promise<CoinflipRoom | null> {
    const user = await this.userService.getUser(playerA_id);
    if (!user || user.is_banned || Number(user.balance_ton) < stake) {
      return null;
    }

    // Deduct stake from player A
    const success = await this.userService.deductBalance(playerA_id, stake);
    if (!success) {
      return null;
    }

    // Create transaction
    await this.transactionService.createTransaction(
      playerA_id,
      TransactionKind.GAME_BET,
      stake,
      undefined,
      'coinflip',
      { role: 'creator' },
    );

    const room = this.coinflipRepository.create({
      stake,
      playerA_id,
      status: CoinflipStatus.WAITING,
      seed: crypto.randomBytes(32).toString('hex'),
    });

    return this.coinflipRepository.save(room);
  }

  async joinRoom(room_id: number, playerB_id: string): Promise<{
    success: boolean;
    room?: CoinflipRoom;
    winner?: string;
    result?: string;
  }> {
    const room = await this.coinflipRepository.findOne({ where: { id: room_id } });
    if (!room || room.status !== CoinflipStatus.WAITING || room.playerA_id === playerB_id) {
      return { success: false };
    }

    const user = await this.userService.getUser(playerB_id);
    if (!user || user.is_banned || Number(user.balance_ton) < room.stake) {
      return { success: false };
    }

    // Deduct stake from player B
    const success = await this.userService.deductBalance(playerB_id, room.stake);
    if (!success) {
      return { success: false };
    }

    // Create transaction for player B
    await this.transactionService.createTransaction(
      playerB_id,
      TransactionKind.GAME_BET,
      room.stake,
      undefined,
      'coinflip',
      { role: 'joiner', room_id },
    );

    // Update room
    room.playerB_id = playerB_id;
    room.status = CoinflipStatus.PLAYING;

    // Play the game
    const result = await this.playGame(room);
    
    await this.coinflipRepository.save(room);
    
    return {
      success: true,
      room,
      winner: result.winner,
      result: result.coinResult,
    };
  }

  private async playGame(room: CoinflipRoom): Promise<{ winner: string; coinResult: string }> {
    // Generate random result using seed
    const hash = crypto.createHash('sha256').update(room.seed + room.id.toString()).digest('hex');
    const random = parseInt(hash.substring(0, 8), 16) / 0xffffffff;
    
    const coinResult = random < 0.5 ? 'heads' : 'tails';
    const winner = random < 0.5 ? room.playerA_id : room.playerB_id;
    
    // Calculate payout (total pot minus 10% rake)
    const totalPot = room.stake * 2;
    const rake = totalPot * 0.1;
    const payout = totalPot - rake;

    // Update room
    room.result = coinResult;
    room.winner_id = winner;
    room.status = CoinflipStatus.FINISHED;

    // Pay winner
    await this.userService.updateBalance(winner, payout);
    await this.transactionService.createTransaction(
      winner,
      TransactionKind.GAME_WIN,
      payout,
      undefined,
      'coinflip',
      {
        room_id: room.id,
        result: coinResult,
        opponent: winner === room.playerA_id ? room.playerB_id : room.playerA_id,
      },
    );

    return { winner, coinResult };
  }

  async getWaitingRooms(): Promise<CoinflipRoom[]> {
    return this.coinflipRepository.find({
      where: { status: CoinflipStatus.WAITING },
      order: { created_at: 'DESC' },
      take: 20,
    });
  }

  async getRecentGames(): Promise<CoinflipRoom[]> {
    return this.coinflipRepository.find({
      where: { status: CoinflipStatus.FINISHED },
      order: { updated_at: 'DESC' },
      take: 10,
    });
  }

  async getUserRooms(user_id: string): Promise<CoinflipRoom[]> {
    return this.coinflipRepository
      .createQueryBuilder('room')
      .where('room.playerA_id = :user_id OR room.playerB_id = :user_id', { user_id })
      .orderBy('room.created_at', 'DESC')
      .take(20)
      .getMany();
  }

  async getRoomById(room_id: number): Promise<CoinflipRoom | null> {
    return this.coinflipRepository.findOne({ where: { id: room_id } });
  }
}
