"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const transaction_entity_1 = require("./transaction.entity");
let TransactionService = class TransactionService {
    transactionRepository;
    constructor(transactionRepository) {
        this.transactionRepository = transactionRepository;
    }
    async createTransaction(user_id, kind, amount_ton, ref, game_type, game_data) {
        const transaction = this.transactionRepository.create({
            user_id,
            kind,
            amount_ton,
            ref: ref || undefined,
            game_type: game_type || undefined,
            game_data: game_data ? JSON.stringify(game_data) : undefined,
        });
        return this.transactionRepository.save(transaction);
    }
    async getUserTransactions(user_id, limit = 50) {
        return this.transactionRepository.find({
            where: { user_id },
            order: { created_at: 'DESC' },
            take: limit,
        });
    }
    async getAllTransactions(limit = 100) {
        return this.transactionRepository.find({
            order: { created_at: 'DESC' },
            take: limit,
            relations: ['user'],
        });
    }
    async getTransactionsByKind(kind, limit = 50) {
        return this.transactionRepository.find({
            where: { kind },
            order: { created_at: 'DESC' },
            take: limit,
            relations: ['user'],
        });
    }
    async getTransactionsByGame(game_type, limit = 50) {
        return this.transactionRepository.find({
            where: { game_type },
            order: { created_at: 'DESC' },
            take: limit,
            relations: ['user'],
        });
    }
    async getUserGameStats(user_id) {
        const transactions = await this.transactionRepository.find({
            where: { user_id },
            order: { created_at: 'DESC' },
        });
        const stats = {
            total_bets: 0,
            total_wins: 0,
            total_bet_amount: 0,
            total_win_amount: 0,
            games: {},
        };
        for (const tx of transactions) {
            if (tx.kind === transaction_entity_1.TransactionKind.GAME_BET) {
                stats.total_bets++;
                stats.total_bet_amount += Number(tx.amount_ton);
                if (!stats.games[tx.game_type]) {
                    stats.games[tx.game_type] = { bets: 0, wins: 0, bet_amount: 0, win_amount: 0 };
                }
                stats.games[tx.game_type].bets++;
                stats.games[tx.game_type].bet_amount += Number(tx.amount_ton);
            }
            else if (tx.kind === transaction_entity_1.TransactionKind.GAME_WIN) {
                stats.total_wins++;
                stats.total_win_amount += Number(tx.amount_ton);
                if (!stats.games[tx.game_type]) {
                    stats.games[tx.game_type] = { bets: 0, wins: 0, bet_amount: 0, win_amount: 0 };
                }
                stats.games[tx.game_type].wins++;
                stats.games[tx.game_type].win_amount += Number(tx.amount_ton);
            }
        }
        return stats;
    }
};
exports.TransactionService = TransactionService;
exports.TransactionService = TransactionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(transaction_entity_1.Transaction)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], TransactionService);
//# sourceMappingURL=transaction.service.js.map