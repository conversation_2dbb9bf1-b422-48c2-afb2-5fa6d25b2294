import { TransactionService } from './transaction.service';
import { TransactionKind } from './transaction.entity';
export declare class TransactionController {
    private readonly transactionService;
    constructor(transactionService: TransactionService);
    createTransaction(body: {
        user_id: string;
        kind: TransactionKind;
        amount_ton: number;
        ref?: string;
        game_type?: string;
        game_data?: any;
    }): Promise<import("./transaction.entity").Transaction>;
    getUserTransactions(user_id: string, limit?: string): Promise<import("./transaction.entity").Transaction[]>;
    getAllTransactions(limit?: string): Promise<import("./transaction.entity").Transaction[]>;
    getTransactionsByKind(kind: TransactionKind, limit?: string): Promise<import("./transaction.entity").Transaction[]>;
    getTransactionsByGame(game_type: string, limit?: string): Promise<import("./transaction.entity").Transaction[]>;
    getUserGameStats(user_id: string): Promise<any>;
}
