import { Repository } from 'typeorm';
import { GlobalConfig } from './global-config.entity';
export declare class ConfigService {
    private configRepository;
    constructor(configRepository: Repository<GlobalConfig>);
    getConfig(key: string): Promise<string | null>;
    setConfig(key: string, value: string): Promise<GlobalConfig>;
    getWinModifier(): Promise<number>;
    setWinModifier(modifier: number): Promise<void>;
    getAllConfigs(): Promise<GlobalConfig[]>;
    initializeDefaults(): Promise<void>;
}
