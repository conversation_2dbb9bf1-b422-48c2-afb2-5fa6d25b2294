import { PriceService } from './price.service';
export declare class PriceController {
    private readonly priceService;
    constructor(priceService: PriceService);
    getAllPrices(): Promise<import("./price.entity").Price[]>;
    getPrice(model_key: string): Promise<import("./price.entity").Price>;
    updatePrice(body: {
        model_key: string;
        min_ton: number;
        image_url: string;
    }): Promise<import("./price.entity").Price>;
    importPrices(file: Express.Multer.File): Promise<{
        success: boolean;
        imported: number;
        errors: string[];
    }>;
    clearAllPrices(): Promise<{
        success: boolean;
    }>;
}
