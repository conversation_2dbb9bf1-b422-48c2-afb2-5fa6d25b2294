import "./chunk-PR4QN5HX.js";

// node_modules/valibot/dist/index.js
var store;
function getGlobalConfig(config2) {
  return {
    lang: (config2 == null ? void 0 : config2.lang) ?? (store == null ? void 0 : store.lang),
    message: config2 == null ? void 0 : config2.message,
    abortEarly: (config2 == null ? void 0 : config2.abortEarly) ?? (store == null ? void 0 : store.abortEarly),
    abortPipeEarly: (config2 == null ? void 0 : config2.abortPipeEarly) ?? (store == null ? void 0 : store.abortPipeEarly)
  };
}
var store2;
function getGlobalMessage(lang) {
  return store2 == null ? void 0 : store2.get(lang);
}
var store3;
function getSchemaMessage(lang) {
  return store3 == null ? void 0 : store3.get(lang);
}
var store4;
function getSpecificMessage(reference, lang) {
  var _a2;
  return (_a2 = store4 == null ? void 0 : store4.get(reference)) == null ? void 0 : _a2.get(lang);
}
function _stringify(input) {
  var _a2, _b;
  const type = typeof input;
  if (type === "string") {
    return `"${input}"`;
  }
  if (type === "number" || type === "bigint" || type === "boolean") {
    return `${input}`;
  }
  if (type === "object" || type === "function") {
    return (input && ((_b = (_a2 = Object.getPrototypeOf(input)) == null ? void 0 : _a2.constructor) == null ? void 0 : _b.name)) ?? "null";
  }
  return type;
}
function _addIssue(context, label, dataset, config2, other) {
  const input = other && "input" in other ? other.input : dataset.value;
  const expected = (other == null ? void 0 : other.expected) ?? context.expects ?? null;
  const received = (other == null ? void 0 : other.received) ?? _stringify(input);
  const issue = {
    kind: context.kind,
    type: context.type,
    input,
    expected,
    received,
    message: `Invalid ${label}: ${expected ? `Expected ${expected} but r` : "R"}eceived ${received}`,
    requirement: context.requirement,
    path: other == null ? void 0 : other.path,
    issues: other == null ? void 0 : other.issues,
    lang: config2.lang,
    abortEarly: config2.abortEarly,
    abortPipeEarly: config2.abortPipeEarly
  };
  const isSchema = context.kind === "schema";
  const message = (other == null ? void 0 : other.message) ?? context.message ?? getSpecificMessage(context.reference, issue.lang) ?? (isSchema ? getSchemaMessage(issue.lang) : null) ?? config2.message ?? getGlobalMessage(issue.lang);
  if (message !== void 0) {
    issue.message = typeof message === "function" ? (
      // @ts-expect-error
      message(issue)
    ) : message;
  }
  if (isSchema) {
    dataset.typed = false;
  }
  if (dataset.issues) {
    dataset.issues.push(issue);
  } else {
    dataset.issues = [issue];
  }
}
function _getStandardProps(context) {
  return {
    version: 1,
    vendor: "valibot",
    validate(value2) {
      return context["~run"]({ value: value2 }, getGlobalConfig());
    }
  };
}
function _isValidObjectKey(object2, key) {
  return Object.hasOwn(object2, key) && key !== "__proto__" && key !== "prototype" && key !== "constructor";
}
function _joinExpects(values2, separator) {
  const list = [...new Set(values2)];
  if (list.length > 1) {
    return `(${list.join(` ${separator} `)})`;
  }
  return list[0] ?? "never";
}
var ValiError = class extends Error {
  /**
   * Creates a Valibot error with useful information.
   *
   * @param issues The error issues.
   */
  constructor(issues) {
    super(issues[0].message);
    this.name = "ValiError";
    this.issues = issues;
  }
};
var EMOJI_REGEX = (
  // eslint-disable-next-line redos-detector/no-unsafe-regex, regexp/no-dupe-disjunctions -- false positives
  new RegExp("^(?:[\\u{1F1E6}-\\u{1F1FF}]{2}|\\u{1F3F4}[\\u{E0061}-\\u{E007A}]{2}[\\u{E0030}-\\u{E0039}\\u{E0061}-\\u{E007A}]{1,3}\\u{E007F}|(?:\\p{Emoji}\\uFE0F\\u20E3?|\\p{Emoji_Modifier_Base}\\p{Emoji_Modifier}?|\\p{Emoji_Presentation})(?:\\u200D(?:\\p{Emoji}\\uFE0F\\u20E3?|\\p{Emoji_Modifier_Base}\\p{Emoji_Modifier}?|\\p{Emoji_Presentation}))*)+$", "u")
);
function integer(message) {
  return {
    kind: "validation",
    type: "integer",
    reference: integer,
    async: false,
    expects: null,
    requirement: Number.isInteger,
    message,
    "~run"(dataset, config2) {
      if (dataset.typed && !this.requirement(dataset.value)) {
        _addIssue(this, "integer", dataset, config2);
      }
      return dataset;
    }
  };
}
function transform(operation) {
  return {
    kind: "transformation",
    type: "transform",
    reference: transform,
    async: false,
    operation,
    "~run"(dataset) {
      dataset.value = this.operation(dataset.value);
      return dataset;
    }
  };
}
function getFallback(schema, dataset, config2) {
  return typeof schema.fallback === "function" ? (
    // @ts-expect-error
    schema.fallback(dataset, config2)
  ) : (
    // @ts-expect-error
    schema.fallback
  );
}
function getDefault(schema, dataset, config2) {
  return typeof schema.default === "function" ? (
    // @ts-expect-error
    schema.default(dataset, config2)
  ) : (
    // @ts-expect-error
    schema.default
  );
}
function is(schema, input) {
  return !schema["~run"]({ value: input }, { abortEarly: true }).issues;
}
function any() {
  return {
    kind: "schema",
    type: "any",
    reference: any,
    expects: "any",
    async: false,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset) {
      dataset.typed = true;
      return dataset;
    }
  };
}
function array(item, message) {
  return {
    kind: "schema",
    type: "array",
    reference: array,
    expects: "Array",
    async: false,
    item,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      var _a2;
      const input = dataset.value;
      if (Array.isArray(input)) {
        dataset.typed = true;
        dataset.value = [];
        for (let key = 0; key < input.length; key++) {
          const value2 = input[key];
          const itemDataset = this.item["~run"]({ value: value2 }, config2);
          if (itemDataset.issues) {
            const pathItem = {
              type: "array",
              origin: "value",
              input,
              key,
              value: value2
            };
            for (const issue of itemDataset.issues) {
              if (issue.path) {
                issue.path.unshift(pathItem);
              } else {
                issue.path = [pathItem];
              }
              (_a2 = dataset.issues) == null ? void 0 : _a2.push(issue);
            }
            if (!dataset.issues) {
              dataset.issues = itemDataset.issues;
            }
            if (config2.abortEarly) {
              dataset.typed = false;
              break;
            }
          }
          if (!itemDataset.typed) {
            dataset.typed = false;
          }
          dataset.value.push(itemDataset.value);
        }
      } else {
        _addIssue(this, "type", dataset, config2);
      }
      return dataset;
    }
  };
}
function boolean(message) {
  return {
    kind: "schema",
    type: "boolean",
    reference: boolean,
    expects: "boolean",
    async: false,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      if (typeof dataset.value === "boolean") {
        dataset.typed = true;
      } else {
        _addIssue(this, "type", dataset, config2);
      }
      return dataset;
    }
  };
}
function date(message) {
  return {
    kind: "schema",
    type: "date",
    reference: date,
    expects: "Date",
    async: false,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      if (dataset.value instanceof Date) {
        if (!isNaN(dataset.value)) {
          dataset.typed = true;
        } else {
          _addIssue(this, "type", dataset, config2, {
            received: '"Invalid Date"'
          });
        }
      } else {
        _addIssue(this, "type", dataset, config2);
      }
      return dataset;
    }
  };
}
function function_(message) {
  return {
    kind: "schema",
    type: "function",
    reference: function_,
    expects: "Function",
    async: false,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      if (typeof dataset.value === "function") {
        dataset.typed = true;
      } else {
        _addIssue(this, "type", dataset, config2);
      }
      return dataset;
    }
  };
}
function instance(class_, message) {
  return {
    kind: "schema",
    type: "instance",
    reference: instance,
    expects: class_.name,
    async: false,
    class: class_,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      if (dataset.value instanceof this.class) {
        dataset.typed = true;
      } else {
        _addIssue(this, "type", dataset, config2);
      }
      return dataset;
    }
  };
}
function looseObject(entries, message) {
  return {
    kind: "schema",
    type: "loose_object",
    reference: looseObject,
    expects: "Object",
    async: false,
    entries,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      var _a2;
      const input = dataset.value;
      if (input && typeof input === "object") {
        dataset.typed = true;
        dataset.value = {};
        for (const key in this.entries) {
          const valueSchema = this.entries[key];
          if (key in input || (valueSchema.type === "exact_optional" || valueSchema.type === "optional" || valueSchema.type === "nullish") && // @ts-expect-error
          valueSchema.default !== void 0) {
            const value2 = key in input ? (
              // @ts-expect-error
              input[key]
            ) : getDefault(valueSchema);
            const valueDataset = valueSchema["~run"]({ value: value2 }, config2);
            if (valueDataset.issues) {
              const pathItem = {
                type: "object",
                origin: "value",
                input,
                key,
                value: value2
              };
              for (const issue of valueDataset.issues) {
                if (issue.path) {
                  issue.path.unshift(pathItem);
                } else {
                  issue.path = [pathItem];
                }
                (_a2 = dataset.issues) == null ? void 0 : _a2.push(issue);
              }
              if (!dataset.issues) {
                dataset.issues = valueDataset.issues;
              }
              if (config2.abortEarly) {
                dataset.typed = false;
                break;
              }
            }
            if (!valueDataset.typed) {
              dataset.typed = false;
            }
            dataset.value[key] = valueDataset.value;
          } else if (valueSchema.fallback !== void 0) {
            dataset.value[key] = getFallback(valueSchema);
          } else if (valueSchema.type !== "exact_optional" && valueSchema.type !== "optional" && valueSchema.type !== "nullish") {
            _addIssue(this, "key", dataset, config2, {
              input: void 0,
              expected: `"${key}"`,
              path: [
                {
                  type: "object",
                  origin: "key",
                  input,
                  key,
                  // @ts-expect-error
                  value: input[key]
                }
              ]
            });
            if (config2.abortEarly) {
              break;
            }
          }
        }
        if (!dataset.issues || !config2.abortEarly) {
          for (const key in input) {
            if (_isValidObjectKey(input, key) && !(key in this.entries)) {
              dataset.value[key] = input[key];
            }
          }
        }
      } else {
        _addIssue(this, "type", dataset, config2);
      }
      return dataset;
    }
  };
}
function nullish(wrapped, default_) {
  return {
    kind: "schema",
    type: "nullish",
    reference: nullish,
    expects: `(${wrapped.expects} | null | undefined)`,
    async: false,
    wrapped,
    default: default_,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      if (dataset.value === null || dataset.value === void 0) {
        if (this.default !== void 0) {
          dataset.value = getDefault(this, dataset, config2);
        }
        if (dataset.value === null || dataset.value === void 0) {
          dataset.typed = true;
          return dataset;
        }
      }
      return this.wrapped["~run"](dataset, config2);
    }
  };
}
function number(message) {
  return {
    kind: "schema",
    type: "number",
    reference: number,
    expects: "number",
    async: false,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      if (typeof dataset.value === "number" && !isNaN(dataset.value)) {
        dataset.typed = true;
      } else {
        _addIssue(this, "type", dataset, config2);
      }
      return dataset;
    }
  };
}
function optional(wrapped, default_) {
  return {
    kind: "schema",
    type: "optional",
    reference: optional,
    expects: `(${wrapped.expects} | undefined)`,
    async: false,
    wrapped,
    default: default_,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      if (dataset.value === void 0) {
        if (this.default !== void 0) {
          dataset.value = getDefault(this, dataset, config2);
        }
        if (dataset.value === void 0) {
          dataset.typed = true;
          return dataset;
        }
      }
      return this.wrapped["~run"](dataset, config2);
    }
  };
}
function record(key, value2, message) {
  return {
    kind: "schema",
    type: "record",
    reference: record,
    expects: "Object",
    async: false,
    key,
    value: value2,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      var _a2, _b;
      const input = dataset.value;
      if (input && typeof input === "object") {
        dataset.typed = true;
        dataset.value = {};
        for (const entryKey in input) {
          if (_isValidObjectKey(input, entryKey)) {
            const entryValue = input[entryKey];
            const keyDataset = this.key["~run"]({ value: entryKey }, config2);
            if (keyDataset.issues) {
              const pathItem = {
                type: "object",
                origin: "key",
                input,
                key: entryKey,
                value: entryValue
              };
              for (const issue of keyDataset.issues) {
                issue.path = [pathItem];
                (_a2 = dataset.issues) == null ? void 0 : _a2.push(issue);
              }
              if (!dataset.issues) {
                dataset.issues = keyDataset.issues;
              }
              if (config2.abortEarly) {
                dataset.typed = false;
                break;
              }
            }
            const valueDataset = this.value["~run"](
              { value: entryValue },
              config2
            );
            if (valueDataset.issues) {
              const pathItem = {
                type: "object",
                origin: "value",
                input,
                key: entryKey,
                value: entryValue
              };
              for (const issue of valueDataset.issues) {
                if (issue.path) {
                  issue.path.unshift(pathItem);
                } else {
                  issue.path = [pathItem];
                }
                (_b = dataset.issues) == null ? void 0 : _b.push(issue);
              }
              if (!dataset.issues) {
                dataset.issues = valueDataset.issues;
              }
              if (config2.abortEarly) {
                dataset.typed = false;
                break;
              }
            }
            if (!keyDataset.typed || !valueDataset.typed) {
              dataset.typed = false;
            }
            if (keyDataset.typed) {
              dataset.value[keyDataset.value] = valueDataset.value;
            }
          }
        }
      } else {
        _addIssue(this, "type", dataset, config2);
      }
      return dataset;
    }
  };
}
function string(message) {
  return {
    kind: "schema",
    type: "string",
    reference: string,
    expects: "string",
    async: false,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      if (typeof dataset.value === "string") {
        dataset.typed = true;
      } else {
        _addIssue(this, "type", dataset, config2);
      }
      return dataset;
    }
  };
}
function _subIssues(datasets) {
  let issues;
  if (datasets) {
    for (const dataset of datasets) {
      if (issues) {
        issues.push(...dataset.issues);
      } else {
        issues = dataset.issues;
      }
    }
  }
  return issues;
}
function union(options, message) {
  return {
    kind: "schema",
    type: "union",
    reference: union,
    expects: _joinExpects(
      options.map((option) => option.expects),
      "|"
    ),
    async: false,
    options,
    message,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      let validDataset;
      let typedDatasets;
      let untypedDatasets;
      for (const schema of this.options) {
        const optionDataset = schema["~run"]({ value: dataset.value }, config2);
        if (optionDataset.typed) {
          if (optionDataset.issues) {
            if (typedDatasets) {
              typedDatasets.push(optionDataset);
            } else {
              typedDatasets = [optionDataset];
            }
          } else {
            validDataset = optionDataset;
            break;
          }
        } else {
          if (untypedDatasets) {
            untypedDatasets.push(optionDataset);
          } else {
            untypedDatasets = [optionDataset];
          }
        }
      }
      if (validDataset) {
        return validDataset;
      }
      if (typedDatasets) {
        if (typedDatasets.length === 1) {
          return typedDatasets[0];
        }
        _addIssue(this, "type", dataset, config2, {
          issues: _subIssues(typedDatasets)
        });
        dataset.typed = true;
      } else if ((untypedDatasets == null ? void 0 : untypedDatasets.length) === 1) {
        return untypedDatasets[0];
      } else {
        _addIssue(this, "type", dataset, config2, {
          issues: _subIssues(untypedDatasets)
        });
      }
      return dataset;
    }
  };
}
function unknown() {
  return {
    kind: "schema",
    type: "unknown",
    reference: unknown,
    expects: "unknown",
    async: false,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset) {
      dataset.typed = true;
      return dataset;
    }
  };
}
function parse(schema, input, config2) {
  const dataset = schema["~run"]({ value: input }, getGlobalConfig(config2));
  if (dataset.issues) {
    throw new ValiError(dataset.issues);
  }
  return dataset.value;
}
function pipe(...pipe2) {
  return {
    ...pipe2[0],
    pipe: pipe2,
    get "~standard"() {
      return _getStandardProps(this);
    },
    "~run"(dataset, config2) {
      for (const item of pipe2) {
        if (item.kind !== "metadata") {
          if (dataset.issues && (item.kind === "schema" || item.kind === "transformation")) {
            dataset.typed = false;
            break;
          }
          if (!dataset.issues || !config2.abortEarly && !config2.abortPipeEarly) {
            dataset = item["~run"](dataset, config2);
          }
        }
      }
      return dataset;
    }
  };
}

// node_modules/better-promises/dist/index.js
var V = Object.defineProperty;
var Y = (r2, t, e) => t in r2 ? V(r2, t, { enumerable: true, configurable: true, writable: true, value: e }) : r2[t] = e;
var y = (r2, t, e) => Y(r2, typeof t != "symbol" ? t + "" : t, e);
var $ = Object.defineProperty;
var k = (r2, t, e) => t in r2 ? $(r2, t, { enumerable: true, configurable: true, writable: true, value: e }) : r2[t] = e;
var q = (r2, t, e) => k(r2, t + "", e);
function z(r2, t) {
  return (e) => e instanceof r2 && e.type === t;
}
function P(r2, t) {
  t || (t = []);
  const e = Symbol(r2);
  class c5 extends Error {
    constructor(...s) {
      const a3 = typeof t == "function" ? t(...s) : typeof t == "string" ? [t] : t || [];
      super(...a3), q(this, "type", e), this.name = r2;
    }
  }
  return Object.defineProperty(c5, "name", { value: r2 }), [c5, z(c5, e)];
}
var [G, M] = P("CancelledError", "Promise was canceled");
var [H, N] = P(
  "TimeoutError",
  (r2, t) => [`Timeout reached: ${r2}ms`, { cause: t }]
);
var B = Symbol("Resolved");
function C(r2) {
  return Array.isArray(r2) && r2[0] === B;
}
function J(r2) {
  return [B, r2];
}
function x(r2, t) {
  return r2.reject = t.reject, r2.abort = t.abort, r2;
}
var m = class _m extends Promise {
  constructor(e, c5) {
    let o, s;
    super((a3, i3) => {
      let v3, u3;
      typeof e == "function" ? (v3 = e, u3 = c5) : u3 = e;
      const d3 = [], j4 = (n) => (...h5) => {
        const p4 = n(...h5);
        return d3.forEach((F4) => F4()), p4;
      }, g2 = new AbortController(), { signal: l2 } = g2;
      s = (n) => {
        !l2.aborted && g2.abort(n);
      };
      const b5 = () => l2.reason, w5 = (n) => {
        const h5 = () => {
          n(b5());
        };
        l2.addEventListener("abort", h5, true);
        const p4 = () => {
          l2.removeEventListener("abort", h5, true);
        };
        return d3.push(p4), p4;
      }, D3 = j4((n) => {
        a3(n), s(J(n));
      });
      o = j4((n) => {
        i3(n), s(n);
      }), u3 || (u3 = {});
      const { abortSignal: f4, rejectOnAbort: A2 = true } = u3;
      if (f4)
        if (f4.aborted) {
          const { reason: n } = f4;
          if (A2)
            return o(n);
          s(n);
        } else {
          const n = () => {
            s(f4.reason);
          };
          f4.addEventListener("abort", n), d3.push(() => {
            f4.removeEventListener("abort", n);
          });
        }
      A2 && w5(i3);
      const { timeout: E2 } = u3;
      if (E2) {
        const n = setTimeout(() => {
          s(new H(E2));
        }, E2);
        d3.push(() => {
          clearTimeout(n);
        });
      }
      const L5 = () => l2.aborted, S2 = () => C(b5()), T4 = () => {
        const n = b5();
        return C(n) ? n[1] : void 0;
      };
      try {
        const n = v3 && v3(D3, o, {
          abortReason: b5,
          abortSignal: l2,
          isAborted: L5,
          isResolved: S2,
          onAborted: w5,
          onResolved: (h5) => w5(() => {
            S2() && h5(T4());
          }),
          resolved: T4,
          throwIfAborted() {
            if (L5())
              throw b5();
          }
        });
        n instanceof Promise && n.catch(o);
      } catch (n) {
        o(n);
      }
    });
    y(this, "abort");
    y(this, "reject");
    this.abort = s, this.reject = o;
  }
  /**
   * Creates a new AbortablePromise instance using an executor, resolving the promise when a result
   * was returned.
   * @param fn - function returning promise result.
   * @param options - additional options.
   */
  static fn(e, c5) {
    return new _m(async (o, s, a3) => {
      try {
        o(await e(a3));
      } catch (i3) {
        s(i3);
      }
    }, c5);
  }
  static resolve(e) {
    return this.fn(() => e);
  }
  /**
   * @see Promise.reject
   */
  static reject(e) {
    return new _m((c5, o) => {
      o(e);
    });
  }
  /**
   * Aborts the promise with the cancel error.
   */
  cancel() {
    this.abort(new G());
  }
  /**
   * @see Promise.catch
   */
  catch(e) {
    return this.then(void 0, e);
  }
  /**
   * @see Promise.finally
   */
  finally(e) {
    return x(super.finally(e), this);
  }
  /**
   * @see Promise.then
   */
  then(e, c5) {
    return x(super.then(e, c5), this);
  }
};
function I(r2, t) {
  return r2.resolve = t.resolve, r2;
}
var R = class _R extends m {
  constructor(e, c5) {
    let o, s;
    typeof e == "function" ? (o = e, s = c5) : s = e;
    let a3;
    super((i3, v3, u3) => {
      a3 = i3, o && o(i3, v3, u3);
    }, s);
    y(this, "resolve");
    this.resolve = a3;
  }
  /**
   * Creates a new ManualPromise instance using an executor, resolving the promise when a result
   * was returned.
   * @param fn - function returning promise result.
   * @param options - additional options.
   */
  static fn(e, c5) {
    return new _R((o, s, a3) => {
      try {
        Promise.resolve(e(a3)).then(o, s);
      } catch (i3) {
        s(i3);
      }
    }, c5);
  }
  static resolve(e) {
    return this.fn(() => e);
  }
  /**
   * @see Promise.reject
   */
  static reject(e) {
    return new _R((c5, o) => {
      o(e);
    });
  }
  /**
   * @see Promise.catch
   */
  catch(e) {
    return this.then(void 0, e);
  }
  /**
   * @see Promise.finally
   */
  finally(e) {
    return I(super.finally(e), this);
  }
  /**
   * @see Promise.then
   */
  then(e, c5) {
    return I(super.then(e, c5), this);
  }
};

// node_modules/@telegram-apps/toolkit/dist/index.js
function y2(o) {
  return o.replace(/[A-Z]/g, (e) => `-${e.toLowerCase()}`);
}
function $2(o) {
  return o.replace(/_[a-z]/g, (e) => e[1].toUpperCase());
}
function h(o) {
  return Object.entries(o).reduce((e, [r2, t]) => (e[$2(r2)] = t, e), {});
}
function f(o) {
  const e = h(o);
  for (const r2 in e) {
    const t = e[r2];
    t && typeof t == "object" && !(t instanceof Date) && (e[r2] = Array.isArray(t) ? t.map(f) : f(t));
  }
  return e;
}
function k2(o) {
  return o.replace(/_([a-z])/g, (e, r2) => `-${r2.toLowerCase()}`);
}
function g(o) {
  return `tapps/${o}`;
}
function w(o, e) {
  sessionStorage.setItem(g(o), JSON.stringify(e));
}
function T(o) {
  const e = sessionStorage.getItem(g(o));
  try {
    return e ? JSON.parse(e) : void 0;
  } catch {
  }
}
function L(...o) {
  const e = o.flat(1);
  return [
    e.push.bind(e),
    () => {
      e.forEach((r2) => {
        r2();
      });
    }
  ];
}
function O(o, e) {
  e || (e = {});
  const {
    textColor: r2,
    bgColor: t,
    shouldLog: s
  } = e, c5 = s === void 0 ? true : s, d3 = typeof c5 == "boolean" ? () => c5 : c5, u3 = (n, a3, ...i3) => {
    if (a3 || d3()) {
      const l2 = "font-weight:bold;padding:0 5px;border-radius:100px", [b5, m3, p4] = {
        log: ["#0089c3", "white", "INFO"],
        error: ["#ff0000F0", "white", "ERR"],
        warn: ["#D38E15", "white", "WARN"]
      }[n];
      console[n](
        `%c${p4} ${Intl.DateTimeFormat("en-GB", {
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          fractionalSecondDigits: 3,
          timeZone: "UTC"
        }).format(/* @__PURE__ */ new Date())}%c %c${o}`,
        `${l2};background-color:${b5};color:${m3}`,
        "",
        `${l2};${r2 ? `color:${r2};` : ""}${t ? `background-color:${t}` : ""}`,
        ...i3
      );
    }
  };
  return [
    ["log", "forceLog"],
    ["warn", "forceWarn"],
    ["error", "forceError"]
  ].reduce((n, [a3, i3]) => (n[a3] = u3.bind(void 0, a3, false), n[i3] = u3.bind(void 0, a3, true), n), {});
}

// node_modules/@telegram-apps/transformers/dist/index.js
var b;
function N2(e) {
  return {
    lang: (e == null ? void 0 : e.lang) ?? (b == null ? void 0 : b.lang),
    message: e == null ? void 0 : e.message,
    abortEarly: (e == null ? void 0 : e.abortEarly) ?? (b == null ? void 0 : b.abortEarly),
    abortPipeEarly: (e == null ? void 0 : e.abortPipeEarly) ?? (b == null ? void 0 : b.abortPipeEarly)
  };
}
var D;
function Z(e) {
  return D == null ? void 0 : D.get(e);
}
var P2;
function ee(e) {
  return P2 == null ? void 0 : P2.get(e);
}
var j;
function ne(e, n) {
  var r2;
  return (r2 = j == null ? void 0 : j.get(e)) == null ? void 0 : r2.get(n);
}
function re(e) {
  var r2, t;
  const n = typeof e;
  return n === "string" ? `"${e}"` : n === "number" || n === "bigint" || n === "boolean" ? `${e}` : n === "object" || n === "function" ? (e && ((t = (r2 = Object.getPrototypeOf(e)) == null ? void 0 : r2.constructor) == null ? void 0 : t.name)) ?? "null" : n;
}
function f2(e, n, r2, t, s) {
  const u3 = s && "input" in s ? s.input : r2.value, i3 = (s == null ? void 0 : s.expected) ?? e.expects ?? null, l2 = (s == null ? void 0 : s.received) ?? re(u3), o = {
    kind: e.kind,
    type: e.type,
    input: u3,
    expected: i3,
    received: l2,
    message: `Invalid ${n}: ${i3 ? `Expected ${i3} but r` : "R"}eceived ${l2}`,
    requirement: e.requirement,
    path: s == null ? void 0 : s.path,
    issues: s == null ? void 0 : s.issues,
    lang: t.lang,
    abortEarly: t.abortEarly,
    abortPipeEarly: t.abortPipeEarly
  }, y5 = e.kind === "schema", p4 = (s == null ? void 0 : s.message) ?? e.message ?? ne(e.reference, o.lang) ?? (y5 ? ee(o.lang) : null) ?? t.message ?? Z(o.lang);
  p4 && (o.message = typeof p4 == "function" ? (
    // @ts-expect-error
    p4(o)
  ) : p4), y5 && (r2.typed = false), r2.issues ? r2.issues.push(o) : r2.issues = [o];
}
function h2(e) {
  return {
    version: 1,
    vendor: "valibot",
    validate(n) {
      return e["~run"]({ value: n }, N2());
    }
  };
}
function W(e, n) {
  return Object.hasOwn(e, n) && n !== "__proto__" && n !== "prototype" && n !== "constructor";
}
function te(e, n) {
  const r2 = [...new Set(e)];
  return r2.length > 1 ? `(${r2.join(` ${n} `)})` : r2[0] ?? "never";
}
var se = class extends Error {
  /**
   * Creates a Valibot error with useful information.
   *
   * @param issues The error issues.
   */
  constructor(e) {
    super(e[0].message), this.name = "ValiError", this.issues = e;
  }
};
function L2(e, n) {
  return {
    kind: "validation",
    type: "check",
    reference: L2,
    async: false,
    expects: null,
    requirement: e,
    message: n,
    "~run"(r2, t) {
      return r2.typed && !this.requirement(r2.value) && f2(this, "input", r2, t), r2;
    }
  };
}
function R2(e) {
  return {
    kind: "validation",
    type: "integer",
    reference: R2,
    async: false,
    expects: null,
    requirement: Number.isInteger,
    message: e,
    "~run"(n, r2) {
      return n.typed && !this.requirement(n.value) && f2(this, "integer", n, r2), n;
    }
  };
}
function d(e) {
  return {
    kind: "transformation",
    type: "transform",
    reference: d,
    async: false,
    operation: e,
    "~run"(n) {
      return n.value = this.operation(n.value), n;
    }
  };
}
function U(e, n, r2) {
  return typeof e.default == "function" ? (
    // @ts-expect-error
    e.default(n, r2)
  ) : (
    // @ts-expect-error
    e.default
  );
}
function ie(e, n) {
  return !e["~run"]({ value: n }, { abortEarly: true }).issues;
}
function _(e) {
  return {
    kind: "schema",
    type: "boolean",
    reference: _,
    expects: "boolean",
    async: false,
    message: e,
    get "~standard"() {
      return h2(this);
    },
    "~run"(n, r2) {
      return typeof n.value == "boolean" ? n.typed = true : f2(this, "type", n, r2), n;
    }
  };
}
function z2(e) {
  return {
    kind: "schema",
    type: "date",
    reference: z2,
    expects: "Date",
    async: false,
    message: e,
    get "~standard"() {
      return h2(this);
    },
    "~run"(n, r2) {
      return n.value instanceof Date ? isNaN(n.value) ? f2(this, "type", n, r2, {
        received: '"Invalid Date"'
      }) : n.typed = true : f2(this, "type", n, r2), n;
    }
  };
}
function J2(e, n) {
  return {
    kind: "schema",
    type: "instance",
    reference: J2,
    expects: e.name,
    async: false,
    class: e,
    message: n,
    get "~standard"() {
      return h2(this);
    },
    "~run"(r2, t) {
      return r2.value instanceof this.class ? r2.typed = true : f2(this, "type", r2, t), r2;
    }
  };
}
function A(e) {
  return {
    kind: "schema",
    type: "lazy",
    reference: A,
    expects: "unknown",
    async: false,
    getter: e,
    get "~standard"() {
      return h2(this);
    },
    "~run"(n, r2) {
      return this.getter(n.value)["~run"](n, r2);
    }
  };
}
function k3(e, n) {
  return {
    kind: "schema",
    type: "loose_object",
    reference: k3,
    expects: "Object",
    async: false,
    entries: e,
    message: n,
    get "~standard"() {
      return h2(this);
    },
    "~run"(r2, t) {
      var u3;
      const s = r2.value;
      if (s && typeof s == "object") {
        r2.typed = true, r2.value = {};
        for (const i3 in this.entries) {
          const l2 = this.entries[i3];
          if (i3 in s || (l2.type === "exact_optional" || l2.type === "optional" || l2.type === "nullish") && // @ts-expect-error
          l2.default !== void 0) {
            const o = i3 in s ? (
              // @ts-expect-error
              s[i3]
            ) : U(l2), y5 = l2["~run"]({ value: o }, t);
            if (y5.issues) {
              const p4 = {
                type: "object",
                origin: "value",
                input: s,
                key: i3,
                value: o
              };
              for (const m3 of y5.issues)
                m3.path ? m3.path.unshift(p4) : m3.path = [p4], (u3 = r2.issues) == null || u3.push(m3);
              if (r2.issues || (r2.issues = y5.issues), t.abortEarly) {
                r2.typed = false;
                break;
              }
            }
            y5.typed || (r2.typed = false), r2.value[i3] = y5.value;
          } else if (l2.type !== "exact_optional" && l2.type !== "optional" && l2.type !== "nullish" && (f2(this, "key", r2, t, {
            input: void 0,
            expected: `"${i3}"`,
            path: [
              {
                type: "object",
                origin: "key",
                input: s,
                key: i3,
                // @ts-expect-error
                value: s[i3]
              }
            ]
          }), t.abortEarly))
            break;
        }
        if (!r2.issues || !t.abortEarly)
          for (const i3 in s)
            W(s, i3) && !(i3 in this.entries) && (r2.value[i3] = s[i3]);
      } else
        f2(this, "type", r2, t);
      return r2;
    }
  };
}
function E(e) {
  return {
    kind: "schema",
    type: "number",
    reference: E,
    expects: "number",
    async: false,
    message: e,
    get "~standard"() {
      return h2(this);
    },
    "~run"(n, r2) {
      return typeof n.value == "number" && !isNaN(n.value) ? n.typed = true : f2(this, "type", n, r2), n;
    }
  };
}
function c(e, n) {
  return {
    kind: "schema",
    type: "optional",
    reference: c,
    expects: `(${e.expects} | undefined)`,
    async: false,
    wrapped: e,
    default: n,
    get "~standard"() {
      return h2(this);
    },
    "~run"(r2, t) {
      return r2.value === void 0 && (this.default !== void 0 && (r2.value = U(this, r2, t)), r2.value === void 0) ? (r2.typed = true, r2) : this.wrapped["~run"](r2, t);
    }
  };
}
function V2(e, n, r2) {
  return {
    kind: "schema",
    type: "record",
    reference: V2,
    expects: "Object",
    async: false,
    key: e,
    value: n,
    message: r2,
    get "~standard"() {
      return h2(this);
    },
    "~run"(t, s) {
      var i3, l2;
      const u3 = t.value;
      if (u3 && typeof u3 == "object") {
        t.typed = true, t.value = {};
        for (const o in u3)
          if (W(u3, o)) {
            const y5 = u3[o], p4 = this.key["~run"]({ value: o }, s);
            if (p4.issues) {
              const S2 = {
                type: "object",
                origin: "key",
                input: u3,
                key: o,
                value: y5
              };
              for (const g2 of p4.issues)
                g2.path = [S2], (i3 = t.issues) == null || i3.push(g2);
              if (t.issues || (t.issues = p4.issues), s.abortEarly) {
                t.typed = false;
                break;
              }
            }
            const m3 = this.value["~run"](
              { value: y5 },
              s
            );
            if (m3.issues) {
              const S2 = {
                type: "object",
                origin: "value",
                input: u3,
                key: o,
                value: y5
              };
              for (const g2 of m3.issues)
                g2.path ? g2.path.unshift(S2) : g2.path = [S2], (l2 = t.issues) == null || l2.push(g2);
              if (t.issues || (t.issues = m3.issues), s.abortEarly) {
                t.typed = false;
                break;
              }
            }
            (!p4.typed || !m3.typed) && (t.typed = false), p4.typed && (t.value[p4.value] = m3.value);
          }
      } else
        f2(this, "type", t, s);
      return t;
    }
  };
}
function a(e) {
  return {
    kind: "schema",
    type: "string",
    reference: a,
    expects: "string",
    async: false,
    message: e,
    get "~standard"() {
      return h2(this);
    },
    "~run"(n, r2) {
      return typeof n.value == "string" ? n.typed = true : f2(this, "type", n, r2), n;
    }
  };
}
function O2(e) {
  let n;
  if (e)
    for (const r2 of e)
      n ? n.push(...r2.issues) : n = r2.issues;
  return n;
}
function $3(e, n) {
  return {
    kind: "schema",
    type: "union",
    reference: $3,
    expects: te(
      e.map((r2) => r2.expects),
      "|"
    ),
    async: false,
    options: e,
    message: n,
    get "~standard"() {
      return h2(this);
    },
    "~run"(r2, t) {
      let s, u3, i3;
      for (const l2 of this.options) {
        const o = l2["~run"]({ value: r2.value }, t);
        if (o.typed)
          if (o.issues)
            u3 ? u3.push(o) : u3 = [o];
          else {
            s = o;
            break;
          }
        else
          i3 ? i3.push(o) : i3 = [o];
      }
      if (s)
        return s;
      if (u3) {
        if (u3.length === 1)
          return u3[0];
        f2(this, "type", r2, t, {
          issues: O2(u3)
        }), r2.typed = true;
      } else {
        if ((i3 == null ? void 0 : i3.length) === 1)
          return i3[0];
        f2(this, "type", r2, t, {
          issues: O2(i3)
        });
      }
      return r2;
    }
  };
}
function q2() {
  return {
    kind: "schema",
    type: "unknown",
    reference: q2,
    expects: "unknown",
    async: false,
    get "~standard"() {
      return h2(this);
    },
    "~run"(e) {
      return e.typed = true, e;
    }
  };
}
function B2(e, n, r2) {
  const t = e["~run"]({ value: n }, N2(r2));
  if (t.issues)
    throw new se(t.issues);
  return t.value;
}
function v(...e) {
  return {
    ...e[0],
    pipe: e,
    get "~standard"() {
      return h2(this);
    },
    "~run"(n, r2) {
      for (const t of e)
        if (t.kind !== "metadata") {
          if (n.issues && (t.kind === "schema" || t.kind === "transformation")) {
            n.typed = false;
            break;
          }
          (!n.issues || !r2.abortEarly && !r2.abortPipeEarly) && (n = t["~run"](n, r2));
        }
      return n;
    }
  };
}
function ue(e) {
  return e.replace(/_[a-z]/g, (n) => n[1].toUpperCase());
}
function ae(e) {
  return Object.entries(e).reduce((n, [r2, t]) => (n[ue(r2)] = t, n), {});
}
function w2(e) {
  const n = ae(e);
  for (const r2 in n) {
    const t = n[r2];
    t && typeof t == "object" && !(t instanceof Date) && (n[r2] = Array.isArray(t) ? t.map(w2) : w2(t));
  }
  return n;
}
function I2(e) {
  return d((n) => e ? w2(n) : n);
}
function M2(e) {
  return (n) => v(
    e,
    I2(n)
  );
}
function T2(e) {
  return (n, r2) => B2(
    v(e, I2(r2)),
    n
  );
}
function oe() {
  return d(JSON.parse);
}
function C2(e) {
  const n = M2(e);
  return (r2) => v(
    a(),
    oe(),
    n(r2)
  );
}
function ce(e) {
  return d((n) => {
    const r2 = {};
    return new URLSearchParams(n).forEach((t, s) => {
      const u3 = r2[s];
      Array.isArray(u3) ? u3.push(t) : u3 === void 0 ? r2[s] = t : r2[s] = [u3, t];
    }), B2(e, r2);
  });
}
function K(e) {
  return (n) => v(
    $3([a(), J2(URLSearchParams)]),
    ce(e),
    I2(n)
  );
}
var Q = c(A(() => he()));
var le = k3({
  id: E(),
  photo_url: c(a()),
  type: a(),
  title: a(),
  username: c(a())
});
var pe = k3({
  added_to_attachment_menu: c(_()),
  allows_write_to_pm: c(_()),
  first_name: a(),
  id: E(),
  is_bot: c(_()),
  is_premium: c(_()),
  last_name: c(a()),
  language_code: c(a()),
  photo_url: c(a()),
  username: c(a())
});
var fe = k3({
  auth_date: v(
    a(),
    d((e) => new Date(Number(e) * 1e3)),
    z2()
  ),
  can_send_after: c(v(a(), d(Number), R2())),
  chat: c(A(() => ye())),
  chat_type: c(a()),
  chat_instance: c(a()),
  hash: a(),
  query_id: c(a()),
  receiver: Q,
  start_param: c(a()),
  signature: a(),
  user: Q
});
var ye = C2(le);
var he = C2(pe);
var F = K(fe);
function H2(e) {
  return /^#[\da-f]{6}$/i.test(e);
}
function me(e) {
  return /^#[\da-f]{3}$/i.test(e);
}
function ge(e) {
  const n = e.replace(/\s/g, "").toLowerCase();
  if (H2(n))
    return n;
  if (me(n)) {
    let t = "#";
    for (let s = 0; s < 3; s += 1)
      t += n[1 + s].repeat(2);
    return t;
  }
  const r2 = n.match(/^rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)$/) || n.match(/^rgba\((\d{1,3}),(\d{1,3}),(\d{1,3}),\d{1,3}\)$/);
  if (!r2)
    throw new Error(`Value "${e}" does not satisfy any of known RGB formats.`);
  return r2.slice(1).reduce((t, s) => t + parseInt(s, 10).toString(16).padStart(2, "0"), "#");
}
var be = M2(
  V2(
    a(),
    v(
      $3([a(), E()]),
      d((e) => typeof e == "number" ? `#${(e & 16777215).toString(16).padStart(6, "0")}` : e),
      L2(H2)
    )
  )
);
var x2 = c(
  v(a(), d((e) => e === "1"))
);
var G2 = C2(be());
var de = k3({
  tgWebAppBotInline: x2,
  tgWebAppData: c(F()),
  tgWebAppDefaultColors: c(G2()),
  tgWebAppFullscreen: x2,
  tgWebAppPlatform: a(),
  tgWebAppShowSettings: x2,
  tgWebAppStartParam: c(a()),
  tgWebAppThemeParams: G2(),
  tgWebAppVersion: a()
});
var X = K(de);
var ke = T2(F());
var _e = T2(X());
var Se = k3({
  eventType: a(),
  eventData: c(q2())
});
function Y2(e, n) {
  return n || (n = (r2, t) => JSON.stringify(t)), new URLSearchParams(
    Object.entries(e).reduce((r2, [t, s]) => (Array.isArray(s) ? r2.push(...s.map((u3) => [t, String(u3)])) : s != null && r2.push([
      t,
      s instanceof Date ? (s.getTime() / 1e3 | 0).toString() : typeof s == "string" || typeof s == "number" ? String(s) : typeof s == "boolean" ? s ? "1" : "0" : n(t, s)
    ]), r2), [])
  ).toString();
}
function ve(e) {
  return Y2(e);
}
function Ee(e) {
  return Y2(e, (n, r2) => n === "tgWebAppData" ? ve(r2) : JSON.stringify(r2));
}
function De(e) {
  try {
    return ie(X(), e);
  } catch {
    return false;
  }
}

// node_modules/mitt/dist/mitt.mjs
function mitt_default(n) {
  return { all: n = n || /* @__PURE__ */ new Map(), on: function(t, e) {
    var i3 = n.get(t);
    i3 ? i3.push(e) : n.set(t, [e]);
  }, off: function(t, e) {
    var i3 = n.get(t);
    i3 && (e ? i3.splice(i3.indexOf(e) >>> 0, 1) : n.set(t, []));
  }, emit: function(t, e) {
    var i3 = n.get(t);
    i3 && i3.slice().map(function(n2) {
      n2(e);
    }), (i3 = n.get("*")) && i3.slice().map(function(n2) {
      n2(t, e);
    });
  } };
}

// node_modules/@telegram-apps/signals/dist/index.js
var r;
function y3(e, c5) {
  r && r.set(e, c5) || c5();
}
function m2(e) {
  if (r)
    return e();
  r = /* @__PURE__ */ new Map();
  try {
    e();
  } finally {
    r.forEach((c5) => c5()), r = void 0;
  }
}
function S(e, c5) {
  c5 || (c5 = {});
  const g2 = c5.equals || Object.is;
  let u3 = [], s = e;
  const i3 = (t) => {
    if (!g2(s, t)) {
      const l2 = s;
      s = t, y3(o, () => {
        [...u3].forEach(([f4, d3]) => {
          f4(t, l2), d3 && n(f4, true);
        });
      });
    }
  };
  function a3(t) {
    const l2 = typeof t != "object" ? { once: t } : t;
    return {
      once: l2.once || false,
      signal: l2.signal || false
    };
  }
  const n = (t, l2) => {
    const f4 = a3(l2), d3 = u3.findIndex(([h5, p4]) => h5 === t && p4.once === f4.once && p4.signal === f4.signal);
    d3 >= 0 && u3.splice(d3, 1);
  }, o = Object.assign(
    function() {
      return j2(o), s;
    },
    {
      destroy() {
        u3 = [];
      },
      set: i3,
      reset() {
        i3(e);
      },
      sub(t, l2) {
        return u3.push([t, a3(l2)]), () => n(t, l2);
      },
      unsub: n,
      unsubAll() {
        u3 = u3.filter((t) => t[1].signal);
      }
    }
  );
  return o;
}
var b2 = [];
function j2(e) {
  b2.length && b2[b2.length - 1].add(e);
}
function x3(e, c5) {
  let g2 = /* @__PURE__ */ new Set(), u3;
  function s() {
    return u3 || (u3 = S(a3(), c5));
  }
  function i3() {
    s().set(a3());
  }
  function a3() {
    g2.forEach((t) => {
      t.unsub(i3, { signal: true });
    });
    const n = /* @__PURE__ */ new Set();
    let o;
    b2.push(n);
    try {
      o = e();
    } finally {
      b2.pop();
    }
    return n.forEach((t) => {
      t.sub(i3, { signal: true });
    }), g2 = n, o;
  }
  return Object.assign(function() {
    return s()();
  }, {
    destroy() {
      s().destroy();
    },
    sub(...n) {
      return s().sub(...n);
    },
    unsub(...n) {
      s().unsub(...n);
    },
    unsubAll(...n) {
      s().unsubAll(...n);
    }
  });
}

// node_modules/error-kid/dist/index.js
var f3 = Object.defineProperty;
var u = (r2, t, e) => t in r2 ? f3(r2, t, { enumerable: true, configurable: true, writable: true, value: e }) : r2[t] = e;
var c2 = (r2, t, e) => u(r2, typeof t != "symbol" ? t + "" : t, e);
function a2(r2, t) {
  return (e) => e instanceof r2 && e.type === t;
}
function p(r2, t) {
  t || (t = []);
  const e = Symbol(r2);
  class n extends Error {
    constructor(...i3) {
      const o = typeof t == "function" ? t(...i3) : typeof t == "string" ? [t] : t || [];
      super(...o);
      c2(this, "type", e);
      this.name = r2;
    }
  }
  return Object.defineProperty(n, "name", { value: r2 }), [n, a2(n, e)];
}
function l(r2, t, e) {
  const n = Symbol(r2);
  class s extends p(r2, e)[0] {
    constructor(...o) {
      super(...o);
      c2(this, "data");
      c2(this, "type", n);
      this.data = t(...o);
    }
  }
  return Object.defineProperty(s, "name", { value: r2 }), [s, a2(s, n)];
}

// node_modules/@telegram-apps/bridge/dist/index.js
function Q2(e) {
  return is(
    looseObject({ TelegramWebviewProxy: looseObject({ postEvent: function_() }) }),
    e
  );
}
function B3() {
  try {
    return window.self !== window.top;
  } catch {
    return true;
  }
}
function se2(e, t) {
  const r2 = mitt_default(), o = /* @__PURE__ */ new Map(), s = (n, a3, c5) => {
    c5 || (c5 = false);
    const i3 = o.get(n) || /* @__PURE__ */ new Map();
    o.set(n, i3);
    const _2 = i3.get(a3) || [];
    i3.set(a3, _2);
    const l2 = _2.findIndex((m3) => m3[1] === c5);
    l2 >= 0 && (r2.off(n, _2[l2][0]), _2.splice(l2, 1), !_2.length && i3.delete(a3), i3.size || (o.delete(n), !o.size && t()));
  };
  return [
    function(a3, c5, i3) {
      !o.size && e();
      function _2() {
        s(a3, c5, i3);
      }
      function l2(...x4) {
        i3 && _2(), a3 === "*" ? c5(x4) : c5(...x4);
      }
      r2.on(a3, l2);
      const m3 = o.get(a3) || /* @__PURE__ */ new Map();
      o.set(a3, m3);
      const M3 = m3.get(c5) || [];
      return m3.set(c5, M3), M3.push([l2, i3 || false]), _2;
    },
    s,
    // eslint-disable-next-line @typescript-eslint/unbound-method
    r2.emit,
    function() {
      const a3 = r2.all.size;
      r2.all.clear(), o.clear(), a3 && t();
    }
  ];
}
function T3(e, t) {
  window.dispatchEvent(new MessageEvent("message", {
    data: JSON.stringify({ eventType: e, eventData: t }),
    // We specify window.parent to imitate the case, the parent iframe sent us this event.
    source: window.parent
  }));
}
var h3 = false;
var L3 = (e) => {
  w3().log("Event received:", e);
};
function ce2(e) {
  e !== h3 && (h3 = e, h3 ? K2("*", L3) : pe2("*", L3));
}
var w3 = S(O("Bridge", {
  bgColor: "#9147ff",
  textColor: "white",
  shouldLog() {
    return h3;
  }
}));
var ie2 = {
  clipboard_text_received: looseObject({
    req_id: string(),
    data: nullish(string())
  }),
  custom_method_invoked: looseObject({
    req_id: string(),
    result: optional(unknown()),
    error: optional(string())
  }),
  popup_closed: nullish(
    looseObject({ button_id: nullish(string(), () => {
    }) }),
    {}
  ),
  viewport_changed: looseObject({
    height: number(),
    width: nullish(number(), () => window.innerWidth),
    is_state_stable: boolean(),
    is_expanded: boolean()
  }),
  theme_changed: looseObject({
    theme_params: be()
  })
};
function C3(e) {
  if (e.source !== window.parent)
    return;
  let t;
  try {
    t = parse(pipe(string(), oe(), Se), e.data);
  } catch {
    return;
  }
  const { eventType: r2, eventData: o } = t, s = ie2[r2];
  let n;
  try {
    n = s ? parse(s, o) : o;
  } catch (a3) {
    return w3().forceError(
      [
        `An error occurred processing the "${r2}" event from the Telegram application.`,
        "Please, file an issue here:",
        "https://github.com/Telegram-Mini-Apps/telegram-apps/issues/new/choose"
      ].join(`
`),
      t,
      a3
    );
  }
  _e2(r2, n);
}
var [
  K2,
  pe2,
  _e2,
  ue2
] = se2(
  () => {
    const e = window, t = { receiveEvent: T3 };
    e.TelegramGameProxy_receiveEvent = T3, e.TelegramGameProxy = t, e.Telegram = { WebView: t }, window.addEventListener("message", C3);
  },
  () => {
    ["TelegramGameProxy_receiveEvent", "TelegramGameProxy", "Telegram"].forEach((e) => {
      delete window[e];
    }), window.removeEventListener("message", C3);
  }
);
var [
  le2,
  qe
] = p(
  "MethodUnsupportedError",
  (e, t) => [
    `Method "${e}" is unsupported in Mini Apps version ${t}`
  ]
);
var [
  we,
  Te
] = p(
  "MethodParameterUnsupportedError",
  (e, t, r2) => [
    `Parameter "${t}" of "${e}" method is unsupported in Mini Apps version ${r2}`
  ]
);
var [
  fe2,
  Le
] = l(
  "LaunchParamsRetrieveError",
  (e) => ({ errors: e }),
  (e) => [
    [
      "Unable to retrieve launch parameters from any known source. Perhaps, you have opened your app outside Telegram?",
      "📖 Refer to docs for more information:",
      "https://docs.telegram-mini-apps.com/packages/telegram-apps-bridge/environment",
      "",
      "Collected errors:",
      ...e.map(([t, r2]) => `Source: ${t} / ${r2 instanceof Error ? r2.message : String(r2)}`)
    ].join(`
`)
  ]
);
var [
  me2,
  Ce
] = p(
  "InvalidLaunchParamsError",
  (e, t) => [
    `Invalid value for launch params: ${e}`,
    { cause: t }
  ]
);
var [be2, Ue] = p("UnknownEnvError");
var [
  ge2,
  We
] = p(
  "InvokeCustomMethodError",
  (e) => [`Server returned error: ${e}`]
);
var b3 = S((...e) => {
  try {
    window.parent.postMessage(...e);
  } catch (t) {
    t instanceof SyntaxError ? w3().forceError(
      "Unable to call window.parent.postMessage due to incorrectly configured target origin. Use the setTargetOrigin method to allow this origin to receive events",
      t
    ) : w3().forceError(t);
  }
});
var de2 = (...e) => b3()(...e);
var k4 = S("https://web.telegram.org");
function Y3(e, t) {
  w3().log("Posting event:", t ? { eventType: e, eventData: t } : { eventType: e });
  const r2 = window, o = JSON.stringify({ eventType: e, eventData: t });
  if (B3())
    return de2(o, k4());
  if (Q2(r2)) {
    r2.TelegramWebviewProxy.postEvent(e, JSON.stringify(t));
    return;
  }
  if (is(looseObject({ external: looseObject({ notify: function_() }) }), r2)) {
    r2.external.notify(o);
    return;
  }
  throw new be2();
}
function F2(e, t, r2) {
  r2 || (r2 = {});
  const { capture: o } = r2, [s, n] = L();
  return new m((a3) => {
    (Array.isArray(t) ? t : [t]).forEach((c5) => {
      s(
        K2(c5, (i3) => {
          (!o || (Array.isArray(t) ? o({
            event: c5,
            payload: i3
          }) : o(i3))) && a3(i3);
        })
      );
    }), (r2.postEvent || Y3)(e, r2.params);
  }, r2).finally(n);
}
var U2 = "launchParams";
function W2(e) {
  return e.replace(/^[^?#]*[?#]/, "").replace(/[?#]/g, "&");
}
function H3() {
  const e = [];
  for (const [t, r2] of [
    // Try to retrieve launch parameters from the current location. This method can return
    // nothing in case, location was changed, and then the page was reloaded.
    [() => W2(window.location.href), "window.location.href"],
    // Then, try using the lower level API - window.performance.
    [() => {
      const o = performance.getEntriesByType("navigation")[0];
      return o && W2(o.name);
    }, "performance navigation entries"],
    [() => T(U2), "local storage"]
  ]) {
    const o = t();
    if (!o) {
      e.push([r2, new Error("Source is empty")]);
      continue;
    }
    if (De(o))
      return w(U2, o), o;
    try {
      _e(o);
    } catch (s) {
      e.push([r2, s]);
    }
  }
  throw new fe2(e);
}
function he2(e) {
  const t = _e(H3());
  return e ? f(t) : t;
}
function Re(e, t) {
  if (!e)
    try {
      return he2(), true;
    } catch {
      return false;
    }
  return m.fn(async (r2) => {
    if (Q2(window))
      return true;
    try {
      return await F2("web_app_request_theme", "theme_changed", r2), true;
    } catch {
      return false;
    }
  }, t || { timeout: 100 });
}
function $e({ launchParams: e, onEvent: t, resetPostMessage: r2 } = {}) {
  if (e) {
    const n = typeof e == "string" || e instanceof URLSearchParams ? e.toString() : (
      // Here we have to trick serializeLaunchParamsQuery into thinking, it serializes a valid
      // value. We are doing it because we are working with tgWebAppData presented as a
      // string, not an object as serializeLaunchParamsQuery requires.
      Ee({ ...e, tgWebAppData: void 0 }) + (e.tgWebAppData ? `&tgWebAppData=${encodeURIComponent(e.tgWebAppData.toString())}` : "")
    );
    if (!De(n))
      try {
        _e(n);
      } catch (a3) {
        throw new me2(n, a3);
      }
    w("launchParams", n);
  }
  if (B3()) {
    if (!t)
      return;
    const n = pipe(
      string(),
      oe(),
      Se
    );
    r2 && b3.reset();
    const a3 = b3();
    b3.set((...c5) => {
      const [i3] = c5, _2 = () => {
        a3(...c5);
      };
      if (is(n, i3)) {
        const l2 = parse(n, i3);
        t([l2.eventType, l2.eventData], _2);
      } else
        _2();
    });
    return;
  }
  const o = window.TelegramWebviewProxy || {}, s = o.postEvent || (() => {
  });
  window.TelegramWebviewProxy = {
    ...o,
    postEvent(n, a3) {
      const c5 = () => {
        s(n, a3);
      };
      t ? t([n, a3 ? JSON.parse(a3) : void 0], c5) : c5();
    }
  }, w3().log("Environment was mocked by the mockTelegramEnv function");
}
function je() {
  return new URLSearchParams(H3()).get("tgWebAppData") || void 0;
}
function ve2(e) {
  return ({ req_id: t }) => t === e;
}
function I3(e) {
  return e.split(".").map(Number);
}
function ye2(e, t) {
  const r2 = I3(e), o = I3(t), s = Math.max(r2.length, o.length);
  for (let n = 0; n < s; n += 1) {
    const a3 = r2[n] || 0, c5 = o[n] || 0;
    if (a3 !== c5)
      return a3 > c5 ? 1 : -1;
  }
  return 0;
}
function p2(e, t) {
  return ye2(e, t) <= 0;
}
function R3(e, t, r2) {
  if (typeof r2 == "string") {
    if (e === "web_app_open_link") {
      if (t === "try_instant_view")
        return p2("6.4", r2);
      if (t === "try_browser")
        return p2("7.6", r2);
    }
    if (e === "web_app_set_header_color" && t === "color")
      return p2("6.9", r2);
    if (e === "web_app_close" && t === "return_back")
      return p2("7.6", r2);
    if (e === "web_app_setup_main_button" && t === "has_shine_effect")
      return p2("7.10", r2);
  }
  switch (e) {
    case "web_app_open_tg_link":
    case "web_app_open_invoice":
    case "web_app_setup_back_button":
    case "web_app_set_background_color":
    case "web_app_set_header_color":
    case "web_app_trigger_haptic_feedback":
      return p2("6.1", t);
    case "web_app_open_popup":
      return p2("6.2", t);
    case "web_app_close_scan_qr_popup":
    case "web_app_open_scan_qr_popup":
    case "web_app_read_text_from_clipboard":
      return p2("6.4", t);
    case "web_app_switch_inline_query":
      return p2("6.7", t);
    case "web_app_invoke_custom_method":
    case "web_app_request_write_access":
    case "web_app_request_phone":
      return p2("6.9", t);
    case "web_app_setup_settings_button":
      return p2("6.10", t);
    case "web_app_biometry_get_info":
    case "web_app_biometry_open_settings":
    case "web_app_biometry_request_access":
    case "web_app_biometry_request_auth":
    case "web_app_biometry_update_token":
      return p2("7.2", t);
    case "web_app_setup_swipe_behavior":
      return p2("7.7", t);
    case "web_app_share_to_story":
      return p2("7.8", t);
    case "web_app_setup_secondary_button":
    case "web_app_set_bottom_bar_color":
      return p2("7.10", t);
    case "web_app_request_safe_area":
    case "web_app_request_content_safe_area":
    case "web_app_request_fullscreen":
    case "web_app_exit_fullscreen":
    case "web_app_set_emoji_status":
    case "web_app_add_to_home_screen":
    case "web_app_check_home_screen":
    case "web_app_request_emoji_status_access":
    case "web_app_check_location":
    case "web_app_open_location_settings":
    case "web_app_request_file_download":
    case "web_app_request_location":
    case "web_app_send_prepared_message":
    case "web_app_start_accelerometer":
    case "web_app_start_device_orientation":
    case "web_app_start_gyroscope":
    case "web_app_stop_accelerometer":
    case "web_app_stop_device_orientation":
    case "web_app_stop_gyroscope":
    case "web_app_toggle_orientation_lock":
      return p2("8.0", t);
    default:
      return [
        "iframe_ready",
        "iframe_will_reload",
        "web_app_close",
        "web_app_data_send",
        "web_app_expand",
        "web_app_open_link",
        "web_app_ready",
        "web_app_request_theme",
        "web_app_request_viewport",
        "web_app_setup_main_button",
        "web_app_setup_closing_behavior"
      ].includes(e);
  }
}
function Ne(e, t) {
  t || (t = "strict");
  const r2 = typeof t == "function" ? t : (o) => {
    const { method: s, version: n } = o, a3 = "param" in o ? new we(s, o.param, n) : new le2(s, n);
    if (t === "strict")
      throw a3;
    return w3().forceWarn(a3.message);
  };
  return (o, s) => R3(o, e) ? o === "web_app_set_header_color" && is(looseObject({ color: any() }), s) && !R3(o, "color", e) ? r2({ version: e, method: o, param: "color" }) : Y3(o, s) : r2({ version: e, method: o });
}
function ze(e, t, r2, o) {
  return F2("web_app_invoke_custom_method", "custom_method_invoked", {
    ...o || {},
    params: { method: e, params: t, req_id: r2 },
    capture: ve2(r2)
  }).then(({ result: s, error: n }) => {
    if (n)
      throw new ge2(n);
    return s;
  });
}

// node_modules/@telegram-apps/navigation/dist/index.js
function i() {
  return performance.getEntriesByType("navigation")[0];
}
function c3() {
  const t = i();
  return !!t && t.type === "reload";
}

// node_modules/@telegram-apps/sdk/dist/index.js
function z3(e, t) {
  return S(e, t);
}
function c4(e, t) {
  return x3(e, t);
}
function u2(e, t) {
  const o = z3(e, t);
  return [o, c4(o)];
}
var yo = false;
function Pu(e) {
  yo = e, ce2(e);
}
var Qe = S(O("Bridge", {
  bgColor: "forestgreen",
  textColor: "white",
  shouldLog() {
    return yo;
  }
}));
var je2 = z3(0);
var Bo = z3(Y3);
var [oo, le3] = u2({
  tgWebAppPlatform: "unknown",
  tgWebAppVersion: "0.0"
});
var O3 = c4(() => le3().tgWebAppVersion);
function Tn(e) {
  e || (e = {});
  const { postEvent: t } = e, o = e.launchParams || he2();
  oo.set(o), Bo.set(
    typeof t == "function" ? t : Ne(o.tgWebAppVersion)
  ), Qe().log("The package was configured. Launch params:", oo());
}
function Po() {
  return je2.set(je2() + 1), je2().toString();
}
function W3(e, t, o) {
  return ze(e, t, Po(), {
    ...o || {},
    postEvent: i2
  });
}
var d2 = (e, t, o) => (o || (o = {}), o.postEvent || (o.postEvent = i2), F2(e, t, o));
var i2 = (e, t) => Bo()(e, t);
function v2(e) {
  return c4(() => R3(e, O3()));
}
function L4(e) {
  return [e];
}
var [
  ot,
  Tu
] = p("CSSVarsBoundError", "CSS variables are already bound");
var [
  To,
  ku
] = p("NotAvailableError", L4);
var [
  Ou,
  Iu
] = p("InvalidEnvError", L4);
var [
  Z2,
  Vu
] = p("FunctionNotAvailableError", L4);
var [
  y4,
  qu
] = p(
  "InvalidArgumentsError",
  (e, t) => [e, { cause: t }]
);
var [
  kn,
  xu
] = p("ConcurrentCallError", L4);
var [
  On,
  Nu
] = p(
  "SetEmojiStatusError",
  (e) => [`Failed to set emoji status: ${e}`]
);
var [
  ko,
  Du
] = p("AccessDeniedError", L4);
var [
  In,
  Hu
] = p("FullscreenFailedError", L4);
var [
  Vn,
  Lu
] = p("ShareMessageError", L4);
var [
  st,
  Ru
] = p("UnknownThemeParamsKeyError", (e) => [`Unknown theme params key passed: ${e}`]);
function so() {
  return typeof window > "u";
}
function p3(e, t, o) {
  o || (o = {});
  const {
    isSupported: s,
    isMounted: n,
    isMounting: r2,
    component: a3,
    supports: l2
  } = o || {}, P3 = `${a3 ? `${a3}.` : ""}${e}()`, T4 = s ? Array.isArray(s) || typeof s == "object" && "any" in s ? s : [s] : void 0;
  function X2(g2) {
    if (l2) {
      const _2 = l2[g2];
      return R3(_2[0], _2[1], O3());
    }
    return true;
  }
  function k5() {
    if (!T4)
      return;
    function g2(U3) {
      return typeof U3 == "function" ? U3() : R3(U3, O3()) ? void 0 : `it is unsupported in Mini Apps version ${O3()}`;
    }
    const _2 = Array.isArray(T4) ? T4 : T4.any, x4 = _2.map(g2).filter(Boolean);
    return Array.isArray(T4) ? x4[0] : x4.length === _2.length ? x4[x4.length - 1] : void 0;
  }
  function an(...g2) {
    for (const _2 in l2)
      if (l2[_2][2](...g2) && !X2(_2))
        return `option ${_2} is not supported in Mini Apps version ${O3()}`;
  }
  let _e3;
  if (l2) {
    _e3 = {};
    for (const g2 in l2)
      _e3[g2] = c4(() => X2(g2));
  }
  const Kt = c4(() => !k5()), Yt = c4(() => O3() !== "0.0"), Xt = c4(() => !n || n()), Zt = c4(
    () => Re() && !so() && Yt() && Kt() && Xt()
  );
  return Object.assign(
    (...g2) => {
      const _2 = `Unable to call the ${P3} ${a3 ? "method" : "function"}:`;
      if (so() || !Re())
        throw new Z2(`${_2} it can't be called outside Mini Apps`);
      if (!Yt())
        throw new Z2(`${_2} the SDK was not initialized. Use the SDK init() function`);
      const x4 = k5();
      if (x4)
        throw new Z2(`${_2} ${x4}`);
      const U3 = an(...g2);
      if (U3)
        throw new Z2(`${_2} ${U3}`);
      if (!Xt()) {
        const cn = r2 && r2() ? "mounting. Wait for the mount completion" : `unmounted. Use the ${a3}.mount() method`;
        throw new Z2(`${_2} the component is ${cn}`);
      }
      return t(...g2);
    },
    t,
    {
      isAvailable: Zt,
      ifAvailable(...g2) {
        return Zt() ? [true, t(...g2)] : [false];
      }
    },
    T4 ? { isSupported: Kt } : {},
    _e3 ? { supports: _e3 } : {}
  );
}
function we2(e, t) {
  return t || (t = {}), (o, s, n, r2) => p3(o, s, {
    ...t,
    isSupported: n || t.isSupported,
    supports: r2,
    component: e
  });
}
function I4(e, t, o) {
  return we2(e, { isSupported: o, isMounted: t });
}
function w4(e, t) {
  return we2(e, { isSupported: t });
}
var $e2 = "web_app_setup_back_button";
var Oo = "back_button_pressed";
var Me = "backButton";
var [no, qn] = u2(false);
var [he3, xn] = u2(false);
var Nn = v2($e2);
var Io = I4(Me, he3, $e2);
var nt = w4(Me, $e2);
var Dn = Io("hide", () => {
  rt(false);
});
var Hn = nt("mount", () => {
  he3() || (rt(c3() && T(Me) || false), he3.set(true));
});
function rt(e) {
  e !== no() && (i2($e2, { is_visible: e }), w(Me, e), no.set(e));
}
var Ln = nt(
  "onClick",
  (e) => K2(Oo, e)
);
var Rn = nt(
  "offClick",
  (e) => {
    pe2(Oo, e);
  }
);
var jn = Io("show", () => {
  rt(true);
});
function Fn() {
  he3.set(false);
}
var ju = Object.freeze(Object.defineProperty({
  __proto__: null,
  hide: Dn,
  isMounted: xn,
  isSupported: Nn,
  isVisible: qn,
  mount: Hn,
  offClick: Rn,
  onClick: Ln,
  show: jn,
  unmount: Fn
}, Symbol.toStringTag, { value: "Module" }));
function b4(e, t, o) {
  o || (o = {});
  const {
    promise: s,
    error: n
  } = o, [r2, a3] = s ? [s, c4(s)] : u2(), [l2, P3] = n ? [n, c4(n)] : u2();
  return [
    Object.assign((...T4) => {
      if (r2()) {
        const k5 = new kn(t);
        return l2.set(k5), m.reject(k5);
      }
      m2(() => {
        r2.set(e(...T4)), l2.set(void 0);
      });
      let X2;
      return r2().catch((k5) => {
        throw X2 = k5, k5;
      }).finally(() => {
        m2(() => {
          r2.set(void 0), l2.set(X2);
        });
      });
    }, e),
    [r2, a3, c4(() => !!r2())],
    [l2, P3]
  ];
}
function pe3(e, t, o) {
  const [s, ...n] = b4(t, `The ${e} component is already mounting`), [r2, a3] = u2(false);
  return [
    (...l2) => r2() ? m.resolve() : s(...l2).then((P3) => {
      m2(() => {
        r2.set(true), o(P3);
      });
    }),
    ...n,
    [r2, a3]
  ];
}
var [at, Un] = u2({
  available: false,
  type: "",
  accessGranted: false,
  accessRequested: false,
  deviceId: "",
  tokenSaved: false
});
var Gn = c4(() => at().available);
var ro = "web_app_biometry_get_info";
var zn = p3(
  "requestBiometry",
  (e) => d2(ro, "biometry_info_received", e),
  { isSupported: ro }
);
function Vo(e) {
  if (!M(e))
    throw e;
}
function Q3(e) {
  const t = e();
  t && t.catch(Vo).cancel();
}
var re2 = "biometry";
var Ae = "web_app_biometry_request_auth";
var it = "biometry_info_received";
var qo = (e) => {
  ve3(ct(e));
};
function xo() {
  throw new To("Biometry is not available");
}
function ct(e) {
  let t = false, o = false, s = "", n = false, r2 = "", a3 = false;
  return e.available && (t = true, o = e.token_saved, s = e.device_id, n = e.access_requested, r2 = e.type, a3 = e.access_granted), { available: t, tokenSaved: o, deviceId: s, type: r2, accessGranted: a3, accessRequested: n };
}
var Wn = v2(Ae);
var [
  Qn,
  Kn,
  Yn,
  No
] = pe3(
  re2,
  (e) => {
    const t = c3() && T(re2);
    return t ? m.resolve(t) : zn({ abortSignal: e }).then(ct);
  },
  (e) => {
    K2(it, qo), ve3(e);
  }
);
var Do = w4(re2, Ae);
var ut = I4(re2, No[0], Ae);
var Xn = Do("mount", Qn);
var [, Ho, Zn] = Kn;
var [, Jn] = Yn;
var [er, tr] = No;
var [
  or,
  sr,
  nr
] = b4(
  (e) => m.fn(async (t) => {
    const o = at();
    o.available || xo();
    const s = await d2(Ae, "biometry_auth_requested", {
      ...e,
      ...t,
      params: { reason: ((e || {}).reason || "").trim() }
    }), { token: n } = s;
    return typeof n == "string" && ve3({ ...o, token: n }), s;
  }, e),
  "Biometry authentication is already in progress"
);
var rr = ut("authenticate", or);
var [, Lo, ar] = sr;
var [, ir] = nr;
var cr = Do("openSettings", () => {
  i2("web_app_biometry_open_settings");
});
var [
  ur,
  lr,
  pr
] = b4(
  (e) => m.fn(async (t) => {
    const o = await d2("web_app_biometry_request_access", it, {
      ...e,
      ...t,
      params: { reason: (e || {}).reason || "" }
    }).then(ct);
    return o.available || xo(), ve3(o), o.accessGranted;
  }, e),
  "Biometry access request is already in progress"
);
var dr = ut("requestAccess", ur);
var [, Ro, mr] = lr;
var [, _r] = pr;
function ve3(e) {
  at.set(e), w(re2, e);
}
function fr() {
  [Lo, Ro, Ho].forEach(Q3), pe2(it, qo), er.set(false);
}
var hr = ut(
  "updateToken",
  (e) => (e || (e = {}), d2("web_app_biometry_update_token", "biometry_token_updated", {
    ...e,
    params: {
      token: e.token || "",
      reason: e.reason
    }
  }).then((t) => t.status))
);
var Fu = Object.freeze(Object.defineProperty({
  __proto__: null,
  authError: ir,
  authPromise: Lo,
  authenticate: rr,
  isAuthenticating: ar,
  isAvailable: Gn,
  isMounted: tr,
  isMounting: Zn,
  isRequestingAccess: mr,
  isSupported: Wn,
  mount: Xn,
  mountError: Jn,
  mountPromise: Ho,
  openSettings: cr,
  requestAccess: dr,
  requestAccessError: _r,
  requestAccessPromise: Ro,
  state: Un,
  unmount: fr,
  updateToken: hr
}, Symbol.toStringTag, { value: "Module" }));
function ye3(e, t) {
  return we2(e, { isMounted: t });
}
var V3 = we2;
var Be = "closingBehavior";
var [ao, br] = u2(false);
var [Ke, jo] = u2(false);
var Fo = ye3(Be, jo);
var gr = V3(Be);
var Er = Fo("disableConfirmation", () => {
  lt(false);
});
var Sr = Fo("enableConfirmation", () => {
  lt(true);
});
var Cr = gr("mount", () => {
  Ke() || (lt(
    c3() && T(Be) || false
  ), Ke.set(true));
});
function lt(e) {
  e !== ao() && (i2("web_app_setup_closing_behavior", { need_confirmation: e }), w(Be, e), ao.set(e));
}
function wr() {
  Ke.set(false);
}
var Uu = Object.freeze(Object.defineProperty({
  __proto__: null,
  disableConfirmation: Er,
  enableConfirmation: Sr,
  isConfirmationEnabled: br,
  isMounted: jo,
  mount: Cr,
  unmount: wr
}, Symbol.toStringTag, { value: "Module" }));
var Uo = "web_app_invoke_custom_method";
var de3 = w4("cloudStorage", Uo);
var $r = v2(Uo);
var Go = de3("deleteItem", (e, t) => {
  const o = Array.isArray(e) ? e : [e];
  return o.length ? W3("deleteStorageValues", { keys: o }, t).then() : m.resolve();
});
function Mr(e, t) {
  const o = Array.isArray(e) ? e : [e];
  return o.length ? W3("getStorageValues", { keys: o }, t).then((s) => {
    const n = {
      // Fulfill the response with probably missing keys.
      ...o.reduce((r2, a3) => (r2[a3] = "", r2), {}),
      ...parse(record(string(), string()), s)
    };
    return typeof e == "string" ? n[e] : n;
  }) : m.resolve(Array.isArray(e) ? {} : "");
}
var Ar = de3("getItem", Mr);
var zo = de3("getKeys", (e) => W3("getStorageKeys", {}, e).then(
  (t) => parse(array(string()), t)
));
var vr = de3("setItem", (e, t, o) => W3("saveStorageValue", {
  key: e,
  value: t
}, o).then());
var yr = de3("clear", (e) => zo(e).then(Go));
var Gu = Object.freeze(Object.defineProperty({
  __proto__: null,
  clear: yr,
  deleteItem: Go,
  getItem: Ar,
  getKeys: zo,
  isSupported: $r,
  setItem: vr
}, Symbol.toStringTag, { value: "Module" }));
var me3 = "web_app_trigger_haptic_feedback";
var pt = w4("hapticFeedback", me3);
var Br = v2(me3);
var Pr = pt(
  "impactOccurred",
  (e) => {
    i2(me3, {
      type: "impact",
      impact_style: e
    });
  }
);
var Tr = pt(
  "notificationOccurred",
  (e) => {
    i2(me3, {
      type: "notification",
      notification_type: e
    });
  }
);
var kr = pt(
  "selectionChanged",
  () => {
    i2(me3, { type: "selection_change" });
  }
);
var zu = Object.freeze(Object.defineProperty({
  __proto__: null,
  impactOccurred: Pr,
  isSupported: Br,
  notificationOccurred: Tr,
  selectionChanged: kr
}, Symbol.toStringTag, { value: "Module" }));
var [Wo, Or] = u2(void 0);
function B4(e) {
  return c4(() => {
    const t = Wo();
    return t ? t[e] : void 0;
  });
}
var Qo = B4("auth_date");
var Ko = B4("can_send_after");
var Ir = c4(() => {
  const e = Qo(), t = Ko();
  return t && e ? new Date(e.getTime() + t * 1e3) : void 0;
});
var Vr = B4("chat");
var qr = B4("chat_type");
var xr = B4("chat_instance");
var Nr = B4("hash");
var Dr = B4("query_id");
var [Hr, Lr] = u2();
var Rr = B4("receiver");
function jr() {
  const e = he2();
  Wo.set(e.tgWebAppData), Hr.set(je());
}
var Fr = B4("start_param");
var Ur = B4("user");
var Wu = Object.freeze(Object.defineProperty({
  __proto__: null,
  authDate: Qo,
  canSendAfter: Ko,
  canSendAfterDate: Ir,
  chat: Vr,
  chatInstance: xr,
  chatType: qr,
  hash: Nr,
  queryId: Dr,
  raw: Lr,
  receiver: Rr,
  restore: jr,
  startParam: Fr,
  state: Or,
  user: Ur
}, Symbol.toStringTag, { value: "Module" }));
var dt = "web_app_open_invoice";
var Gr = w4("invoice", dt);
var zr = v2(dt);
function Wr(e, t, o) {
  let s;
  if (t === "url") {
    const { hostname: n, pathname: r2 } = new URL(e, window.location.href);
    if (n !== "t.me")
      throw new y4(`Link has unexpected hostname: ${n}`);
    const a3 = r2.match(/^\/(\$|invoice\/)([A-Za-z0-9\-_=]+)$/);
    if (!a3)
      throw new y4(
        'Expected to receive a link with a pathname in format "/invoice/{slug}" or "/${slug}"'
      );
    [, , s] = a3;
  } else
    s = e, o = t;
  return d2(dt, "invoice_closed", {
    ...o,
    params: { slug: s },
    capture: (n) => s === n.slug
  }).then((n) => n.status);
}
var [
  Qr,
  Kr,
  Yr
] = b4(Wr, "Invoice is already opened");
var Xr = Gr("open", Qr);
var [, Zr, Jr] = Kr;
var [, ea] = Yr;
var Qu = Object.freeze(Object.defineProperty({
  __proto__: null,
  isOpened: Jr,
  isSupported: zr,
  open: Xr,
  openError: ea,
  openPromise: Zr
}, Symbol.toStringTag, { value: "Module" }));
var J3 = "locationManager";
var mt = "web_app_check_location";
var io = "web_app_open_location_settings";
var be3 = z3({
  available: false,
  accessGranted: false,
  accessRequested: false
});
function _t(e) {
  return c4(() => be3()[e]);
}
var ta = v2(mt);
var oa = _t("available");
var sa = _t("accessGranted");
var na = _t("accessRequested");
function ra(e) {
  let t = false, o, s;
  return e.available && (t = true, o = e.access_requested, s = e.access_granted), {
    available: t,
    accessGranted: s || false,
    accessRequested: o || false
  };
}
var [
  aa,
  ia,
  ca,
  Yo
] = pe3(
  J3,
  (e) => {
    const t = c3() && T(J3);
    return t ? m.resolve(t) : d2("web_app_check_location", "location_checked", e).then(ra);
  },
  (e) => {
    be3.set(e), w(J3, e);
  }
);
var Xo = w4(J3, mt);
var ua = I4(J3, Yo[0], mt);
var la = Xo("mount", aa);
var [, pa, da] = ia;
var [, ma] = ca;
var [_a, fa] = Yo;
var [
  ha,
  ba,
  ga
] = b4(
  (e) => d2("web_app_request_location", "location_requested", e).then((t) => {
    if (!t.available)
      throw be3.set({ ...be3(), available: false }), new To("Location data tracking is not available");
    const { available: o, ...s } = t;
    return s;
  }),
  "Location request is currently in progress"
);
var Ea = ua("requestLocation", ha);
var [, Zo, Sa] = ba;
var [, Ca] = ga;
var wa = Xo("openSettings", () => {
  i2(io);
}, io);
function $a() {
  Q3(Zo), _a.set(false);
}
var Ku = Object.freeze(Object.defineProperty({
  __proto__: null,
  isAccessGranted: sa,
  isAccessRequested: na,
  isAvailable: oa,
  isMounted: fa,
  isMounting: da,
  isRequestingLocation: Sa,
  isSupported: ta,
  mount: la,
  mountError: ma,
  mountPromise: pa,
  openSettings: wa,
  requestLocation: Ea,
  requestLocationError: Ca,
  requestLocationPromise: Zo,
  unmount: $a
}, Symbol.toStringTag, { value: "Module" }));
function ft(e) {
  const t = {};
  for (const o in e) {
    const s = e[o];
    s !== void 0 && (t[o] = s);
  }
  return t;
}
function Jo(e) {
  const t = ge(e);
  return Math.sqrt(
    [0.299, 0.587, 0.114].reduce((o, s, n) => {
      const r2 = parseInt(t.slice(1 + n * 2, 1 + (n + 1) * 2), 16);
      return o + r2 * r2 * s;
    }, 0)
  ) < 120;
}
var [Fe, Ma] = u2(false);
var [D2, K3] = u2({});
function h4(e) {
  return c4(() => D2()[e]);
}
var Aa = h4("accent_text_color");
var es = h4("bg_color");
var ht = h4("button_color");
var ts = h4("button_text_color");
var va = h4("bottom_bar_bg_color");
var ya = h4("destructive_text_color");
var Ba = h4("header_bg_color");
var Pa = h4("hint_color");
var Ta = c4(() => {
  const e = es();
  return !e || Jo(e);
});
var ka = h4("link_color");
var os = h4("secondary_bg_color");
var Oa = h4("section_bg_color");
var Ia = h4("section_header_text_color");
var Va = h4("section_separator_color");
var qa = h4("subtitle_text_color");
var xa = h4("text_color");
function R4(e) {
  return c4(() => bt()[e]);
}
var ee2 = z3({
  hasShineEffect: false,
  isEnabled: true,
  isLoaderVisible: false,
  isVisible: false,
  text: "Continue"
});
var bt = c4(() => {
  const e = ee2();
  return {
    ...e,
    backgroundColor: e.backgroundColor || ht() || "#2481cc",
    textColor: e.textColor || ts() || "#ffffff"
  };
});
var [Ye, ss] = u2(false);
var Na = R4("backgroundColor");
var Da = R4("hasShineEffect");
var Ha = R4("isEnabled");
var La = R4("isLoaderVisible");
var Ra = R4("isVisible");
var ja = R4("text");
var Fa = R4("textColor");
var Ua = "web_app_setup_main_button";
var ns = "main_button_pressed";
var Pe = "mainButton";
var gt = V3(Pe);
var Ga = ye3(Pe, ss);
var za = gt("mount", () => {
  if (!Ye()) {
    const e = c3() && T(Pe);
    e && ee2.set(e), Ye.set(true);
  }
});
var Wa = gt(
  "onClick",
  (e) => K2(ns, e)
);
var Qa = gt(
  "offClick",
  (e) => {
    pe2(ns, e);
  }
);
var Ka = Ga(
  "setParams",
  (e) => {
    ee2.set({ ...ee2(), ...ft(e) }), w(Pe, ee2());
    const t = bt();
    t.text && i2(Ua, {
      color: t.backgroundColor,
      has_shine_effect: t.hasShineEffect,
      is_active: t.isEnabled,
      is_progress_visible: t.isLoaderVisible,
      is_visible: t.isVisible,
      text: t.text,
      text_color: t.textColor
    });
  }
);
function Ya() {
  Ye.set(false);
}
var Yu = Object.freeze(Object.defineProperty({
  __proto__: null,
  backgroundColor: Na,
  hasShineEffect: Da,
  isEnabled: Ha,
  isLoaderVisible: La,
  isMounted: ss,
  isVisible: Ra,
  mount: za,
  offClick: Qa,
  onClick: Wa,
  setParams: Ka,
  state: bt,
  text: ja,
  textColor: Fa,
  unmount: Ya
}, Symbol.toStringTag, { value: "Module" }));
function Et(e, t) {
  document.documentElement.style.setProperty(e, t);
}
function St(e) {
  document.documentElement.style.removeProperty(e);
}
var G3 = "themeParams";
var Ct = "theme_changed";
var rs = V3(G3);
var wt = ({ theme_params: e }) => {
  D2.set(e), w(G3, e);
};
var [
  Xa,
  as,
  Za,
  is2
] = pe3(
  G3,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  (e) => m.resolve(
    c3() && T(G3) || le3().tgWebAppThemeParams || {}
  ),
  (e) => {
    K2(Ct, wt), D2.set(e);
  }
);
var Ja = ye3(G3, is2[0]);
var ei = Ja(
  "bindCssVars",
  (e) => {
    if (Fe())
      throw new ot();
    e || (e = (s) => `--tg-theme-${k2(s)}`);
    function t(s) {
      Object.entries(D2()).forEach(([n, r2]) => {
        r2 && s(n, r2);
      });
    }
    function o() {
      t((s, n) => {
        Et(e(s), n);
      });
    }
    return o(), D2.sub(o), Fe.set(true), () => {
      t(St), D2.unsub(o), Fe.set(false);
    };
  }
);
var cs = rs("mount", Xa);
var ti = as[2];
var us = as[1];
var oi = Za[1];
var [Xe, si] = is2;
var ls = rs("mountSync", () => {
  if (!Xe()) {
    const e = c3() && T(G3) || le3().tgWebAppThemeParams || {};
    K2(Ct, wt), m2(() => {
      D2.set(e), Xe.set(true);
    });
  }
});
function ni() {
  Q3(us), pe2(Ct, wt), Xe.set(false);
}
function ps(e) {
  return c4(() => Te2(e()));
}
function Te2(e) {
  return H2(e) ? e : K3()[e];
}
var [ae2, ri] = u2("bg_color");
var $t = ps(ae2);
var [ie3, ai] = u2("bottom_bar_bg_color");
var Mt = c4(() => {
  const e = ie3();
  return H2(e) ? e : K3()[e] || os();
});
var [ce3, ii] = u2("bg_color");
var ds = ps(ce3);
var [Ue2, ci] = u2(false);
var ui = c4(() => {
  const e = $t();
  return e ? Jo(e) : false;
});
var [ke2, li] = u2(true);
var ms = c4(() => ({
  backgroundColor: ae2(),
  bottomBarColor: ie3(),
  headerColor: ce3(),
  isActive: ke2()
}));
var Xu = Object.freeze(Object.defineProperty({
  __proto__: null,
  accentTextColor: Aa,
  backgroundColor: es,
  bindCssVars: ei,
  bottomBarBgColor: va,
  buttonColor: ht,
  buttonTextColor: ts,
  destructiveTextColor: ya,
  headerBackgroundColor: Ba,
  hintColor: Pa,
  isCssVarsBound: Ma,
  isDark: Ta,
  isMounted: si,
  isMounting: ti,
  linkColor: ka,
  mount: cs,
  mountError: oi,
  mountPromise: us,
  mountSync: ls,
  secondaryBackgroundColor: os,
  sectionBackgroundColor: Oa,
  sectionHeaderTextColor: Ia,
  sectionSeparatorColor: Va,
  state: K3,
  subtitleTextColor: qa,
  textColor: xa,
  unmount: ni
}, Symbol.toStringTag, { value: "Module" }));
var ge3 = "web_app_set_background_color";
var Ee2 = "web_app_set_bottom_bar_color";
var N3 = "web_app_set_header_color";
var At = "visibility_changed";
var H4 = "miniApp";
var vt = {
  any: [
    ge3,
    Ee2,
    N3
  ]
};
var pi = c4(() => vt.any.some((e) => R3(e, O3())));
var yt = (e) => {
  ke2.set(e.is_visible), Ie();
};
var Bt = (e) => {
  [
    [ce3, N3],
    [ae2, ge3],
    [ie3, Ee2]
  ].forEach(([t, o]) => {
    const s = t();
    if (!H2(s) && // Header color setter uses additional checks. We don't apply changes if the current
    // value is a known color key because it updates automatically by itself.
    (o !== N3 || s !== "bg_color" && s !== "secondary_bg_color")) {
      const n = e[s];
      n && i2(o, { color: n });
    }
  });
};
var [
  di,
  _s,
  mi,
  fs
] = pe3(
  H4,
  (e) => cs(e).then(() => c3() && T(H4) || void 0),
  (e) => {
    Pt.ifAvailable(e ? e.backgroundColor : "bg_color"), Tt.ifAvailable(e ? e.bottomBarColor : "bottom_bar_bg_color"), kt.ifAvailable(e ? e.headerColor : "bg_color"), ke2.set(e ? e.isActive : true), K2(At, yt), K3.sub(Bt);
  }
);
var hs = V3(H4);
var bs = w4(H4, vt);
var Oe = I4(H4, fs[0], vt);
var _i = Oe(
  "bindCssVars",
  (e) => {
    if (Ue2())
      throw new ot();
    const [t, o] = L();
    function s(n, r2) {
      function a3() {
        Et(n, r2() || null);
      }
      a3(), t(r2.sub(a3), St.bind(null, n));
    }
    return e || (e = (n) => `--tg-${y2(n)}`), s(e("bgColor"), $t), s(e("bottomBarColor"), Mt), s(e("headerColor"), ds), t(() => {
      Ue2.set(false);
    }), Ue2.set(true), o;
  }
);
var fi = hs("close", (e) => {
  i2("web_app_close", { return_back: e });
});
var hi = bs("mount", di);
var bi = _s[2];
var gs = _s[1];
var gi = mi[1];
var [Ze, Ei] = fs;
var Si = bs("mountSync", () => {
  if (!Ze()) {
    ls();
    const e = c3() && T(H4) || void 0;
    Pt.ifAvailable(e ? e.backgroundColor : "bg_color"), Tt.ifAvailable(e ? e.bottomBarColor : "bottom_bar_bg_color"), kt.ifAvailable(e ? e.headerColor : "bg_color"), K2(At, yt), K3.sub(Bt), m2(() => {
      ke2.set(e ? e.isActive : true), Ze.set(true);
    });
  }
});
var Ci = hs("ready", () => {
  i2("web_app_ready");
});
function Ie() {
  w(H4, ms());
}
var Pt = Oe(
  "setBackgroundColor",
  (e) => {
    if (e === ae2())
      return;
    const t = Te2(e);
    if (!t)
      throw new st(e);
    i2(ge3, { color: t }), ae2.set(e), Ie();
  },
  ge3
);
var Tt = Oe(
  "setBottomBarColor",
  (e) => {
    if (e === ie3())
      return;
    const t = Te2(e);
    if (!t)
      throw new st(e);
    i2(Ee2, { color: t }), ie3.set(e), Ie();
  },
  Ee2
);
var kt = Oe(
  "setHeaderColor",
  (e) => {
    if (e !== ce3()) {
      if (e === "bg_color" || e === "secondary_bg_color")
        i2(N3, { color_key: e });
      else {
        const t = Te2(e);
        if (!t)
          throw new st(e);
        i2(N3, { color: t });
      }
      ce3.set(e), Ie();
    }
  },
  N3,
  {
    rgb: [N3, "color", H2]
  }
);
function wi() {
  Q3(gs), pe2(At, yt), K3.unsub(Bt), Ze.set(false);
}
var Zu = Object.freeze(Object.defineProperty({
  __proto__: null,
  backgroundColor: ri,
  backgroundColorRGB: $t,
  bindCssVars: _i,
  bottomBarColor: ai,
  bottomBarColorRGB: Mt,
  close: fi,
  headerColor: ii,
  headerColorRGB: ds,
  isActive: li,
  isCssVarsBound: ci,
  isDark: ui,
  isMounted: Ei,
  isMounting: bi,
  isSupported: pi,
  mount: hi,
  mountError: gi,
  mountPromise: gs,
  mountSync: Si,
  ready: Ci,
  setBackgroundColor: Pt,
  setBottomBarColor: Tt,
  setHeaderColor: kt,
  state: ms,
  unmount: wi
}, Symbol.toStringTag, { value: "Module" }));
function $i(e) {
  const t = e.message.trim(), o = (e.title || "").trim(), s = e.buttons || [];
  if (o.length > 64)
    throw new y4(`Invalid title: ${o}`);
  if (!t || t.length > 256)
    throw new y4(`Invalid message: ${t}`);
  if (s.length > 3)
    throw new y4(`Invalid buttons count: ${s.length}`);
  return {
    title: o,
    message: t,
    buttons: s.length ? s.map((n, r2) => {
      const a3 = n.id || "";
      if (a3.length > 64)
        throw new y4(`Button with index ${r2} has invalid id: ${a3}`);
      if (!n.type || n.type === "default" || n.type === "destructive") {
        const l2 = n.text.trim();
        if (!l2 || l2.length > 64)
          throw new y4(`Button with index ${r2} has invalid text: ${l2}`);
        return { type: n.type, text: l2, id: a3 };
      }
      return { type: n.type, id: a3 };
    }) : [{ type: "close", id: "" }]
  };
}
var Ot = "web_app_open_popup";
var Es = w4("popup", Ot);
var Mi = v2(Ot);
var [Ss, It, Cs] = b4(
  (e) => d2(Ot, "popup_closed", {
    ...e,
    params: $i(e)
  }).then(({ button_id: t }) => t === void 0 ? null : t),
  "A popup is already opened"
);
var Ai = Es("open", Ss);
var vi = It[1];
var yi = It[2];
var Bi = Cs[1];
var Pi = Es("show", Ss);
var [, Ti, ki] = It;
var [, Oi] = Cs;
var Ju = Object.freeze(Object.defineProperty({
  __proto__: null,
  isOpened: yi,
  isShown: ki,
  isSupported: Mi,
  open: Ai,
  openError: Bi,
  openPromise: vi,
  show: Pi,
  showError: Oi,
  showPromise: Ti
}, Symbol.toStringTag, { value: "Module" }));
var ws = "web_app_close_scan_qr_popup";
var Vt = "web_app_open_scan_qr_popup";
var Ii = "scan_qr_popup_closed";
var Vi = "qr_text_received";
var $s = w4("qrScanner", Vt);
var qi = $s("close", () => {
  i2(ws), Q3(Ms);
});
var xi = v2(Vt);
function Ni(e) {
  e || (e = {});
  const { onCaptured: t, text: o, capture: s } = e, [, n] = L(
    K2(Ii, () => {
      r2.resolve();
    }),
    K2(Vi, (a3) => {
      t ? t(a3.data) : (!s || s(a3.data)) && (r2.resolve(a3.data), i2(ws));
    })
  ), r2 = new R(e);
  return (e.postEvent || i2)(Vt, { text: o }), m.resolve(r2).catch(Vo).finally(n);
}
var [
  Di,
  Hi,
  Li
] = b4(Ni, "The QR Scanner is already opened");
var Ri = $s("open", Di);
var [, Ms, ji] = Hi;
var [, Fi] = Li;
var el = Object.freeze(Object.defineProperty({
  __proto__: null,
  close: qi,
  isOpened: ji,
  isSupported: xi,
  open: Ri,
  openError: Fi,
  openPromise: Ms
}, Symbol.toStringTag, { value: "Module" }));
function q3(e) {
  return c4(() => qt()[e]);
}
var te2 = z3({
  hasShineEffect: false,
  isEnabled: true,
  isLoaderVisible: false,
  isVisible: false,
  position: "left",
  text: "Cancel"
});
var qt = c4(() => {
  const e = te2();
  return {
    ...e,
    backgroundColor: e.backgroundColor || Mt() || "#000000",
    textColor: e.textColor || ht() || "#2481cc"
  };
});
var [Je, As] = u2(false);
var Ui = q3("backgroundColor");
var Gi = q3("hasShineEffect");
var zi = q3("isEnabled");
var Wi = q3("isLoaderVisible");
var Qi = q3("isVisible");
var Ki = q3("position");
var Yi = q3("text");
var Xi = q3("textColor");
var Ve = "web_app_setup_secondary_button";
var vs = "secondary_button_pressed";
var qe2 = "secondaryButton";
var xt = w4(qe2, Ve);
var Zi = I4(qe2, As, Ve);
var Ji = v2(Ve);
var ec = xt("mount", () => {
  if (!Je()) {
    const e = c3() && T(qe2);
    e && te2.set(e), Je.set(true);
  }
});
var tc = xt(
  "onClick",
  (e) => K2(vs, e)
);
var oc = xt(
  "offClick",
  (e) => {
    pe2(vs, e);
  }
);
var sc = Zi(
  "setParams",
  (e) => {
    te2.set({ ...te2(), ...ft(e) }), w(qe2, te2());
    const t = qt();
    t.text && i2(Ve, {
      color: t.backgroundColor,
      has_shine_effect: t.hasShineEffect,
      is_active: t.isEnabled,
      is_progress_visible: t.isLoaderVisible,
      is_visible: t.isVisible,
      position: t.position,
      text: t.text,
      text_color: t.textColor
    });
  }
);
function nc() {
  Je.set(false);
}
var tl = Object.freeze(Object.defineProperty({
  __proto__: null,
  backgroundColor: Ui,
  hasShineEffect: Gi,
  isEnabled: zi,
  isLoaderVisible: Wi,
  isMounted: As,
  isSupported: Ji,
  isVisible: Qi,
  mount: ec,
  offClick: oc,
  onClick: tc,
  position: Ki,
  setParams: sc,
  state: qt,
  text: Yi,
  textColor: Xi,
  unmount: nc
}, Symbol.toStringTag, { value: "Module" }));
var xe = "web_app_setup_settings_button";
var ys = "settings_button_pressed";
var Ne2 = "settingsButton";
var [co, rc] = u2(false);
var [Se2, ac] = u2(false);
var ic = v2(xe);
var Nt = w4(Ne2, xe);
var Bs = I4(Ne2, Se2, xe);
var cc = Bs("hide", () => {
  Dt(false);
});
var uc = Nt("mount", () => {
  Se2() || (Dt(c3() && T(Ne2) || false), Se2.set(true));
});
function Dt(e) {
  e !== co() && (i2(xe, { is_visible: e }), w(Ne2, e), co.set(e));
}
var lc = Nt(
  "onClick",
  (e) => K2(ys, e)
);
var pc = Nt(
  "offClick",
  (e) => {
    pe2(ys, e);
  }
);
var dc = Bs("show", () => {
  Dt(true);
});
function mc() {
  Se2.set(false);
}
var ol = Object.freeze(Object.defineProperty({
  __proto__: null,
  hide: cc,
  isMounted: ac,
  isSupported: ic,
  isVisible: rc,
  mount: uc,
  offClick: pc,
  onClick: lc,
  show: dc,
  unmount: mc
}, Symbol.toStringTag, { value: "Module" }));
var De2 = "web_app_setup_swipe_behavior";
var He = "swipeBehavior";
var [ue3, _c] = u2(false);
var fc = v2(De2);
var [et, hc] = u2(true);
var bc = w4(He, De2);
var Ps = I4(He, ue3, De2);
var gc = Ps("disableVertical", () => {
  Ht(false);
});
var Ec = Ps("enableVertical", () => {
  Ht(true);
});
var Sc = bc("mount", () => {
  ue3() || (Ht(
    c3() && T(He) || false,
    true
  ), ue3.set(true));
});
function Ht(e, t) {
  (e !== et() || t) && (i2(De2, { allow_vertical_swipe: e }), w(He, e), et.set(e));
}
function Cc() {
  ue3.set(false);
}
var sl = Object.freeze(Object.defineProperty({
  __proto__: null,
  _isMounted: ue3,
  _isVerticalEnabled: et,
  disableVertical: gc,
  enableVertical: Ec,
  isMounted: _c,
  isSupported: fc,
  isVerticalEnabled: hc,
  mount: Sc,
  unmount: Cc
}, Symbol.toStringTag, { value: "Module" }));
var j3 = "viewport";
var Lt = "fullscreen_changed";
var Rt = "safe_area_changed";
var jt = "content_safe_area_changed";
var Ft = "viewport_changed";
var Ts = V3(j3);
var uo = { left: 0, top: 0, bottom: 0, right: 0 };
function Ge(e) {
  return Math.max(e, 0);
}
var [ze2, ks] = u2({
  contentSafeAreaInsets: uo,
  height: 0,
  isExpanded: false,
  isFullscreen: false,
  safeAreaInsets: uo,
  stableHeight: 0,
  width: 0
});
function F3(e) {
  return c4(() => ks()[e]);
}
var Ut = F3("height");
var Gt = F3("stableHeight");
var Os = F3("width");
var wc = F3("isExpanded");
var $c = c4(() => Ut() === Gt());
function Y4(e) {
  const { height: t, stableHeight: o, width: s } = e;
  ze2.set({
    ...ze2(),
    ...ft({
      ...e,
      height: t ? Ge(t) : void 0,
      width: s ? Ge(s) : void 0,
      stableHeight: o ? Ge(o) : void 0
    })
  }), w(j3, ze2());
}
function Mc() {
  return T(j3);
}
function Le2(e) {
  return c4(() => zt()[e]);
}
var zt = F3("contentSafeAreaInsets");
var Is = Le2("bottom");
var Vs = Le2("left");
var qs = Le2("right");
var xs = Le2("top");
function Re2(e) {
  return c4(() => Wt()[e]);
}
var Wt = F3("safeAreaInsets");
var Ns = Re2("bottom");
var Ds = Re2("left");
var Hs = Re2("right");
var Ls = Re2("top");
var Rs = "web_app_request_safe_area";
var js = w4(j3, Rs);
var lo = js(
  "requestContentSafeAreaInsets",
  (e) => d2("web_app_request_content_safe_area", jt, e)
);
function Ac(e) {
  return d2("web_app_request_viewport", Ft, e);
}
var po = js(
  "requestSafeAreaInsets",
  (e) => d2(Rs, Rt, e)
);
var Fs = (e) => {
  const { height: t } = e;
  Y4({
    isExpanded: e.is_expanded,
    height: t,
    width: e.width,
    stableHeight: e.is_state_stable ? t : void 0
  });
};
var Us = (e) => {
  Y4({ isFullscreen: e.is_fullscreen });
};
var Gs = (e) => {
  Y4({ safeAreaInsets: e });
};
var zs = (e) => {
  Y4({ contentSafeAreaInsets: e });
};
var [
  vc,
  yc,
  Bc,
  Pc
] = pe3(
  j3,
  (e) => {
    const t = c3() && Mc();
    return t ? m.resolve(t) : m.fn(async (o) => {
      const s = await m.all([
        po.isAvailable() ? po(o) : Wt(),
        lo.isAvailable() ? lo(o) : zt()
      ]), n = le3(), r2 = {
        contentSafeAreaInsets: s[1],
        isFullscreen: !!n.tgWebAppFullscreen,
        safeAreaInsets: s[0]
      };
      if (["macos", "tdesktop", "unigram", "webk", "weba", "web"].includes(n.tgWebAppPlatform)) {
        const a3 = window;
        return {
          ...r2,
          height: a3.innerHeight,
          isExpanded: true,
          stableHeight: a3.innerHeight,
          width: a3.innerWidth
        };
      }
      return Ac(o).then((a3) => ({
        ...r2,
        height: a3.height,
        isExpanded: a3.is_expanded,
        stableHeight: a3.is_state_stable ? a3.height : 0,
        width: a3.width
      }));
    }, e);
  },
  (e) => {
    K2(Ft, Fs), K2(Lt, Us), K2(Rt, Gs), K2(jt, zs), Y4(e);
  }
);
var Tc = Ts("mount", vc);
var [, Ws, kc] = yc;
var [, Oc] = Bc;
var [Qt, Ic] = Pc;
function Vc() {
  Q3(Ws), pe2(Ft, Fs), pe2(Lt, Us), pe2(Rt, Gs), pe2(jt, zs), Qt.set(false);
}
var qc = ye3(j3, Qt);
var [We2, xc] = u2(false);
var Nc = qc(
  "bindCssVars",
  (e) => {
    if (We2())
      throw new ot();
    e || (e = (o) => `--tg-viewport-${y2(o)}`);
    const t = [
      ["height", Ut],
      ["stableHeight", Gt],
      ["width", Os],
      ["safeAreaInsetTop", Ls],
      ["safeAreaInsetBottom", Ns],
      ["safeAreaInsetLeft", Ds],
      ["safeAreaInsetRight", Hs],
      ["contentSafeAreaInsetTop", xs],
      ["contentSafeAreaInsetBottom", Is],
      ["contentSafeAreaInsetLeft", Vs],
      ["contentSafeAreaInsetRight", qs]
    ].reduce((o, [s, n]) => {
      const r2 = e(s);
      if (r2) {
        const a3 = () => {
          Et(r2, `${n()}px`);
        };
        o.push([a3, n.sub(a3), r2]);
      }
      return o;
    }, []);
    return t.forEach((o) => {
      o[0]();
    }), We2.set(true), () => {
      t.forEach((o) => {
        o[1](), St(o[2]);
      }), We2.set(false);
    };
  }
);
var Dc = Ts("expand", () => {
  i2("web_app_expand");
});
var Qs = "web_app_request_fullscreen";
var Hc = I4(j3, Qt, Qs);
var Ks = F3("isFullscreen");
var [
  Lc,
  Rc
] = u2();
var [
  jc,
  Fc
] = u2();
function Ys(e, t) {
  return Hc(
    e,
    b4(
      (o) => d2(
        t ? Qs : "web_app_exit_fullscreen",
        [Lt, "fullscreen_failed"],
        o
      ).then((s) => {
        if ("error" in s && s.error !== "ALREADY_FULLSCREEN")
          throw new In(s.error);
        const n = "is_fullscreen" in s ? s.is_fullscreen : true;
        n !== Ks() && Y4({ isFullscreen: n });
      }),
      "Fullscreen mode change is already being requested",
      {
        promise: Lc,
        error: jc
      }
    )[0]
  );
}
var Uc = Ys("requestFullscreen", true);
var Gc = Ys("exitFullscreen");
var nl = Object.freeze(Object.defineProperty({
  __proto__: null,
  bindCssVars: Nc,
  changeFullscreenError: Fc,
  changeFullscreenPromise: Rc,
  contentSafeAreaInsetBottom: Is,
  contentSafeAreaInsetLeft: Vs,
  contentSafeAreaInsetRight: qs,
  contentSafeAreaInsetTop: xs,
  contentSafeAreaInsets: zt,
  exitFullscreen: Gc,
  expand: Dc,
  height: Ut,
  isCssVarsBound: xc,
  isExpanded: wc,
  isFullscreen: Ks,
  isMounted: Ic,
  isMounting: kc,
  isStable: $c,
  mount: Tc,
  mountError: Oc,
  mountPromise: Ws,
  requestFullscreen: Uc,
  safeAreaInsetBottom: Ns,
  safeAreaInsetLeft: Ds,
  safeAreaInsetRight: Hs,
  safeAreaInsetTop: Ls,
  safeAreaInsets: Wt,
  stableHeight: Gt,
  state: ks,
  unmount: Vc,
  width: Os
}, Symbol.toStringTag, { value: "Module" }));
var Xs = "web_app_request_emoji_status_access";
var [
  zc,
  Wc,
  Qc
] = b4((e) => d2(Xs, "emoji_status_access_requested", e).then((t) => t.status), "Emoji status access request is already in progress");
var rl = p3(
  "requestEmojiStatusAccess",
  zc,
  { isSupported: Xs }
);
var [, al, il] = Wc;
var [, cl] = Qc;
var Zs = "web_app_set_emoji_status";
var [
  Kc,
  Yc,
  Xc
] = b4(
  (e, t) => d2(Zs, ["emoji_status_set", "emoji_status_failed"], {
    params: {
      custom_emoji_id: e,
      duration: (t || {}).duration
    },
    ...t
  }).then((o) => {
    if (o && "error" in o)
      throw new On(o.error);
  }),
  "Emoji status set request is currently in progress"
);
var ul = p3("setEmojiStatus", Kc, {
  isSupported: Zs
});
var [, ll, pl] = Yc;
var [, dl] = Xc;
var Zc = { isSupported: "web_app_add_to_home_screen" };
var Js = "home_screen_failed";
var ml = p3(
  "onAddToHomeScreenFailed",
  (e, t) => K2(Js, e, t),
  { isSupported: "web_app_add_to_home_screen" }
);
var _l = p3(
  "offAddToHomeScreenFailed",
  (e) => {
    pe2(Js, e);
  },
  Zc
);
var en = { isSupported: "web_app_add_to_home_screen" };
var tn = "home_screen_added";
var fl = p3(
  "onAddedToHomeScreen",
  (e, t) => K2(tn, e, t),
  en
);
var hl = p3(
  "offAddedToHomeScreen",
  (e) => {
    pe2(tn, e);
  },
  en
);
var mo = "web_app_add_to_home_screen";
var bl = p3(
  "addToHomeScreen",
  () => {
    i2(mo);
  },
  { isSupported: mo }
);
var on = "web_app_check_home_screen";
var [
  Jc,
  eu,
  tu
] = b4((e) => d2(on, "home_screen_checked", e).then((t) => t.status || "unknown"), "Check home screen status request is currently in progress");
var gl = p3("checkHomeScreenStatus", Jc, {
  isSupported: on
});
var [, El, Sl] = eu;
var [, Cl] = tu;
var ou = V3();
var wl = ou(
  "openLink",
  (e, t) => {
    if (typeof e == "string")
      try {
        e = new URL(e);
      } catch (o) {
        throw new y4(`"${e.toString()}" is invalid URL`, o);
      }
    t || (t = {}), i2("web_app_open_link", {
      url: e.toString(),
      try_browser: t.tryBrowser,
      try_instant_view: t.tryInstantView
    });
  }
);
var _o = "web_app_open_tg_link";
var su = V3();
var nu = su(
  "openTelegramLink",
  (e) => {
    const t = e.toString();
    if (!t.match(/^https:\/\/t.me\/.+/))
      throw new y4(`"${t}" is invalid URL`);
    if (!R3(_o, O3())) {
      window.location.href = t;
      return;
    }
    e = new URL(e), i2(_o, { path_full: e.pathname + e.search });
  }
);
var ru = V3();
var $l = ru(
  "shareURL",
  (e, t) => {
    nu(
      "https://t.me/share/url?" + new URLSearchParams({ url: e, text: t || "" }).toString().replace(/\+/g, "%20")
    );
  }
);
function au(e, t) {
  return new m({ abortSignal: t, timeout: e }).catch(() => {
  });
}
var sn = "web_app_request_phone";
var [
  iu,
  cu,
  uu
] = b4((e) => d2(sn, "phone_requested", e).then((t) => t.status), "Phone access request is currently in progress");
var lu = p3("requestPhoneAccess", iu, {
  isSupported: sn
});
var [, Ml, Al] = cu;
var [, vl] = uu;
var nn = {
  isSupported: "web_app_request_phone"
};
async function fo(e) {
  const t = parse(string(), await W3("getRequestedContact", {}, {
    ...e,
    timeout: (e || {}).timeout || 5e3
  }));
  return {
    raw: t,
    parsed: parse(
      pipe(
        // todo: Union is unnecessary here, but we use it to comply TypeScript checker.
        union([string(), instance(URLSearchParams)]),
        ce(
          looseObject({
            contact: pipe(
              string(),
              oe(),
              looseObject({
                user_id: number(),
                phone_number: string(),
                first_name: string(),
                last_name: optional(string())
              })
            ),
            auth_date: pipe(
              string(),
              transform((o) => new Date(Number(o) * 1e3)),
              date()
            ),
            hash: string()
          })
        )
      ),
      t
    )
  };
}
var [pu, du, mu] = b4(
  (e) => new m(
    async (t, o, s) => {
      try {
        return t(await fo(s));
      } catch (a3) {
        if (a3 instanceof ValiError)
          throw a3;
      }
      if (await lu(s) !== "sent")
        throw new ko("User denied access");
      let r2 = 50;
      for (; !s.isAborted(); ) {
        try {
          return t(await fo(s));
        } catch (a3) {
          if (a3 instanceof ValiError)
            throw a3;
        }
        await au(r2), r2 += 50;
      }
    },
    e
  ),
  "Contact is already being requested"
);
var _u = p3("requestContactComplete", pu, nn);
var yl = p3(
  "requestContact",
  (e) => _u(e).then((t) => t.parsed),
  nn
);
var [, Bl, Pl] = du;
var [, Tl] = mu;
var rn = "web_app_request_write_access";
var [
  fu,
  hu,
  bu
] = b4(
  (e) => d2(rn, "write_access_requested", e).then((t) => t.status),
  "Write access request is currently in progress"
);
var kl = p3("requestWriteAccess", fu, {
  isSupported: rn
});
var [, Ol, Il] = hu;
var [, Vl] = bu;
function gu(e) {
  const t = document.createElement("textarea");
  t.value = e, t.style.top = "0", t.style.left = "0", t.style.position = "fixed", document.body.appendChild(t), t.focus(), t.select();
  try {
    document.execCommand("copy");
  } finally {
    document.body.removeChild(t);
  }
}
async function ql(e) {
  try {
    const { clipboard: t } = navigator;
    if (t)
      return await t.writeText(e);
  } catch {
  }
  gu(e);
}
var ho = "web_app_request_file_download";
var xl = p3(
  "downloadFile",
  (e, t, o) => d2(
    ho,
    "file_download_requested",
    { ...o, params: { url: e, file_name: t } }
  ).then((s) => {
    if (s.status !== "downloading")
      throw new ko("User denied the action");
  }),
  { isSupported: ho }
);
var Nl = p3(
  "getCurrentTime",
  (e) => W3("getCurrentTime", {}, e).then((t) => parse(
    pipe(number(), integer(), transform((o) => new Date(o * 1e3)), date()),
    t
  )),
  { isSupported: "web_app_invoke_custom_method" }
);
var bo = "web_app_read_text_from_clipboard";
var Dl = p3(
  "readTextFromClipboard",
  (e) => {
    const t = Po();
    return d2(bo, "clipboard_text_received", {
      ...e,
      params: { req_id: t },
      capture: ve2(t)
    }).then(({ data: o = null }) => o);
  },
  { isSupported: bo }
);
function Eu(e) {
  const t = {}, o = e.match(/Telegram-Android(?:\/([^ ]+))?(?: (\([^)]+\))?|$)/);
  if (o) {
    const [, s, n] = o;
    s && (t.appVersion = s), n && n.slice(1, n.length - 1).split(";").forEach((r2) => {
      const [a3, l2] = r2.trim().split(" ");
      if (a3 === "Android")
        t.androidVersion = l2;
      else if (a3 === "SDK") {
        const P3 = parseInt(l2, 10);
        P3 && (t.sdkVersion = P3);
      } else l2 ? (t.manufacturer = a3, t.model = l2) : t.performanceClass = a3;
    });
  }
  return t;
}
function Hl() {
  return Eu(navigator.userAgent);
}
var go = "web_app_data_send";
var Ll = p3(
  "sendData",
  (e) => {
    const { size: t } = new Blob([e]);
    if (!t || t > 4096)
      throw new y4(t ? "Maximum size of data to send is 4096 bytes" : "Attempted to send empty data");
    i2(go, { data: e });
  },
  { isSupported: go }
);
var Eo = "web_app_send_prepared_message";
var Rl = p3(
  "shareMessage",
  (e, t) => d2(Eo, ["prepared_message_failed", "prepared_message_sent"], {
    ...t,
    params: { id: e }
  }).then((o) => {
    if (o && "error" in o)
      throw new Vn(o.error);
  }),
  { isSupported: Eo }
);
var So = "web_app_share_to_story";
var jl = p3(
  "shareStory",
  (e, t) => {
    t || (t = {}), i2(So, {
      text: t.text,
      media_url: e,
      widget_link: t.widgetLink
    });
  },
  { isSupported: So }
);
var Su = "web_app_switch_inline_query";
var Fl = p3(
  "switchInlineQuery",
  (e, t) => {
    i2(Su, {
      query: e,
      chat_types: t || []
    });
  },
  {
    isSupported() {
      return le3().tgWebAppBotInline ? void 0 : "The application must be launched in the inline mode";
    }
  }
);
function Ul(e) {
  try {
    return [true, e()];
  } catch (t) {
    return [false, t];
  }
}
function Gl(e) {
  Tn(e);
  const [t, o] = L(
    K2("reload_iframe", () => {
      Qe().log("Received a request to reload the page"), i2("iframe_will_reload"), window.location.reload();
    })
  ), { acceptCustomStyles: s = true } = e || {};
  if (s) {
    const n = document.createElement("style");
    n.id = "telegram-custom-styles", document.head.appendChild(n), t(
      K2("set_custom_style", (r2) => {
        n.innerHTML = r2;
      }),
      () => {
        document.head.removeChild(n);
      }
    );
  }
  return i2("iframe_ready", { reload_supported: true }), Qe().log("The package was initialized"), o;
}
export {
  m as AbortablePromise,
  ko as AccessDeniedError,
  ot as CSSVarsBoundError,
  G as CancelledError,
  kn as ConcurrentCallError,
  In as FullscreenFailedError,
  Z2 as FunctionUnavailableError,
  y4 as InvalidArgumentsError,
  Ou as InvalidEnvError,
  me2 as InvalidLaunchParamsError,
  ge2 as InvokeCustomMethodError,
  fe2 as LaunchParamsRetrieveError,
  R as ManualPromise,
  we as MethodParameterUnsupportedError,
  le2 as MethodUnsupportedError,
  To as NotAvailableError,
  On as SetEmojiStatusError,
  Vn as ShareMessageError,
  H as TimeoutError,
  be2 as UnknownEnvError,
  st as UnknownThemeParamsKeyError,
  bl as addToHomeScreen,
  rr as authenticateBiometry,
  ju as backButton,
  _i as bindMiniAppCssVars,
  ei as bindThemeParamsCssVars,
  Nc as bindViewportCssVars,
  Fu as biometry,
  ir as biometryAuthError,
  Lo as biometryAuthPromise,
  Jn as biometryMountError,
  Un as biometryState,
  w3 as bridgeLogger,
  Fc as changeFullscreenError,
  Rc as changeFullscreenPromise,
  gl as checkHomeScreenStatus,
  Cl as checkHomeScreenStatusError,
  El as checkHomeScreenStatusPromise,
  fi as closeMiniApp,
  qi as closeQrScanner,
  Uu as closingBehavior,
  Gu as cloudStorage,
  Tn as configure,
  ql as copyTextToClipboard,
  O as createLogger,
  Ne as createPostEvent,
  Po as createRequestId,
  Go as deleteCloudStorageItem,
  Er as disableClosingConfirmation,
  gc as disableVerticalSwipes,
  xl as downloadFile,
  T3 as emitEvent,
  Sr as enableClosingConfirmation,
  Ec as enableVerticalSwipes,
  Gc as exitFullscreen,
  Dc as expandViewport,
  Ar as getCloudStorageItem,
  zo as getCloudStorageKeys,
  Nl as getCurrentTime,
  zu as hapticFeedback,
  Pr as hapticFeedbackImpactOccurred,
  Tr as hapticFeedbackNotificationOccurred,
  kr as hapticFeedbackSelectionChanged,
  Dn as hideBackButton,
  cc as hideSettingsButton,
  Vo as ignoreCanceled,
  Gl as init,
  Wu as initData,
  Qo as initDataAuthDate,
  Ko as initDataCanSendAfter,
  Ir as initDataCanSendAfterDate,
  Vr as initDataChat,
  xr as initDataChatInstance,
  qr as initDataChatType,
  Nr as initDataHash,
  Dr as initDataQueryId,
  Lr as initDataRaw,
  Rr as initDataReceiver,
  Fr as initDataStartParam,
  Or as initDataState,
  Ur as initDataUser,
  Qu as invoice,
  W3 as invokeCustomMethod,
  Du as isAccessDeniedError,
  ar as isAuthenticatingBiometry,
  xn as isBackButtonMounted,
  Nn as isBackButtonSupported,
  qn as isBackButtonVisible,
  Gn as isBiometryAvailable,
  tr as isBiometryMounted,
  Zn as isBiometryMounting,
  Wn as isBiometrySupported,
  Tu as isCSSVarsBoundError,
  M as isCancelledError,
  Sl as isCheckingHomeScreenStatus,
  jo as isClosingBehaviorMounted,
  br as isClosingConfirmationEnabled,
  $r as isCloudStorageSupported,
  Jo as isColorDark,
  xu as isConcurrentCallError,
  Ks as isFullscreen,
  Hu as isFullscreenFailedError,
  Vu as isFunctionNotAvailableError,
  Br as isHapticFeedbackSupported,
  qu as isInvalidArguments,
  Iu as isInvalidEnvError,
  Ce as isInvalidLaunchParamsError,
  Jr as isInvoiceOpened,
  zr as isInvoiceSupported,
  We as isInvokeCustomMethodError,
  Le as isLaunchParamsRetrieveError,
  sa as isLocationManagerAccessGranted,
  na as isLocationManagerAccessRequested,
  oa as isLocationManagerAvailable,
  fa as isLocationManagerMounted,
  da as isLocationManagerMounting,
  ta as isLocationManagerSupported,
  Ha as isMainButtonEnabled,
  La as isMainButtonLoaderVisible,
  ss as isMainButtonMounted,
  Ra as isMainButtonVisible,
  Te as isMethodMethodParameterUnsupportedError,
  qe as isMethodUnsupportedError,
  li as isMiniAppActive,
  ci as isMiniAppCssVarsBound,
  ui as isMiniAppDark,
  Ei as isMiniAppMounted,
  bi as isMiniAppMounting,
  pi as isMiniAppSupported,
  ku as isNotAvailableError,
  yi as isPopupOpened,
  ki as isPopupShown,
  Mi as isPopupSupported,
  ji as isQrScannerOpened,
  xi as isQrScannerSupported,
  H2 as isRGB,
  me as isRGBShort,
  mr as isRequestingBiometryAccess,
  Pl as isRequestingContact,
  il as isRequestingEmojiStatusAccess,
  Sa as isRequestingLocation,
  Al as isRequestingPhoneAccess,
  Il as isRequestingWriteAccess,
  so as isSSR,
  zi as isSecondaryButtonEnabled,
  Wi as isSecondaryButtonLoaderVisible,
  As as isSecondaryButtonMounted,
  Ji as isSecondaryButtonSupported,
  Qi as isSecondaryButtonVisible,
  Nu as isSetEmojiStatusError,
  pl as isSettingEmojiStatus,
  ac as isSettingsButtonMounted,
  ic as isSettingsButtonSupported,
  rc as isSettingsButtonVisible,
  Lu as isShareMessageError,
  _c as isSwipeBehaviorMounted,
  fc as isSwipeBehaviorSupported,
  Re as isTMA,
  Ma as isThemeParamsCssVarsBound,
  Ta as isThemeParamsDark,
  si as isThemeParamsMounted,
  ti as isThemeParamsMounting,
  N as isTimeoutError,
  Ue as isUnknownEnvError,
  Ru as isUnknownThemeParamsKeyError,
  hc as isVerticalSwipesEnabled,
  xc as isViewportCssVarsBound,
  wc as isViewportExpanded,
  Ic as isViewportMounted,
  kc as isViewportMounting,
  $c as isViewportStable,
  Ku as locationManager,
  ma as locationManagerMountError,
  pa as locationManagerMountPromise,
  Yu as mainButton,
  Na as mainButtonBackgroundColor,
  Da as mainButtonHasShineEffect,
  bt as mainButtonState,
  ja as mainButtonText,
  Fa as mainButtonTextColor,
  Zu as miniApp,
  ri as miniAppBackgroundColor,
  $t as miniAppBackgroundColorRGB,
  ai as miniAppBottomBarColor,
  Mt as miniAppBottomBarColorRGB,
  ii as miniAppHeaderColor,
  ds as miniAppHeaderColorRGB,
  gi as miniAppMountError,
  gs as miniAppMountPromise,
  Ci as miniAppReady,
  ms as miniAppState,
  $e as mockTelegramEnv,
  Hn as mountBackButton,
  Xn as mountBiometry,
  Ho as mountBiometryPromise,
  Cr as mountClosingBehavior,
  la as mountLocationManager,
  za as mountMainButton,
  hi as mountMiniApp,
  Si as mountMiniAppSync,
  ec as mountSecondaryButton,
  uc as mountSettingsButton,
  Sc as mountSwipeBehavior,
  cs as mountThemeParams,
  ls as mountThemeParamsSync,
  Tc as mountViewport,
  pe2 as off,
  _l as offAddToHomeScreenFailed,
  hl as offAddedToHomeScreen,
  Rn as offBackButtonClick,
  Qa as offMainButtonClick,
  oc as offSecondaryButtonClick,
  pc as offSettingsButtonClick,
  K2 as on,
  ml as onAddToHomeScreenFailed,
  fl as onAddedToHomeScreen,
  Ln as onBackButtonClick,
  Wa as onMainButtonClick,
  tc as onSecondaryButtonClick,
  lc as onSettingsButtonClick,
  cr as openBiometrySettings,
  Xr as openInvoice,
  ea as openInvoiceError,
  Zr as openInvoicePromise,
  wl as openLink,
  wa as openLocationManagerSettings,
  Ai as openPopup,
  Bi as openPopupError,
  vi as openPopupPromise,
  Ri as openQrScanner,
  Fi as openQrScannerError,
  Ms as openQrScannerPromise,
  nu as openTelegramLink,
  ke as parseInitDataQuery,
  _e as parseLaunchParamsQuery,
  Ju as popup,
  i2 as postEvent,
  de2 as postMessage,
  b3 as postMessageImplementation,
  el as qrScanner,
  Dl as readTextFromClipboard,
  d2 as request,
  zn as requestBiometry,
  dr as requestBiometryAccess,
  _r as requestBiometryAccessError,
  Ro as requestBiometryAccessPromise,
  yl as requestContact,
  _u as requestContactComplete,
  Tl as requestContactError,
  Bl as requestContactPromise,
  lo as requestContentSafeAreaInsets,
  rl as requestEmojiStatusAccess,
  cl as requestEmojiStatusAccessError,
  al as requestEmojiStatusAccessPromise,
  Uc as requestFullscreen,
  Ea as requestLocation,
  Ca as requestLocationError,
  Zo as requestLocationPromise,
  lu as requestPhoneAccess,
  vl as requestPhoneAccessError,
  Ml as requestPhoneAccessPromise,
  po as requestSafeAreaInsets,
  Ac as requestViewport,
  kl as requestWriteAccess,
  Vl as requestWriteAccessError,
  Ol as requestWriteAccessPromise,
  jr as restoreInitData,
  Hl as retrieveAndroidDeviceData,
  Eu as retrieveAndroidDeviceDataFrom,
  he2 as retrieveLaunchParams,
  je as retrieveRawInitData,
  H3 as retrieveRawLaunchParams,
  Ul as safeCall,
  Qe as sdkLogger,
  tl as secondaryButton,
  Ui as secondaryButtonBackgroundColor,
  Gi as secondaryButtonHasShineEffect,
  Ki as secondaryButtonPosition,
  qt as secondaryButtonState,
  Yi as secondaryButtonText,
  Xi as secondaryButtonTextColor,
  Ll as sendData,
  ve as serializeInitDataQuery,
  Ee as serializeLaunchParamsQuery,
  Y2 as serializeToQuery,
  vr as setCloudStorageItem,
  Pu as setDebug,
  ul as setEmojiStatus,
  dl as setEmojiStatusError,
  ll as setEmojiStatusPromise,
  Ka as setMainButtonParams,
  Pt as setMiniAppBackgroundColor,
  Tt as setMiniAppBottomBarColor,
  kt as setMiniAppHeaderColor,
  sc as setSecondaryButtonParams,
  ol as settingsButton,
  Rl as shareMessage,
  jl as shareStory,
  $l as shareURL,
  jn as showBackButton,
  Pi as showPopup,
  Oi as showPopupError,
  Ti as showPopupPromise,
  dc as showSettingsButton,
  R3 as supports,
  sl as swipeBehavior,
  Fl as switchInlineQuery,
  k4 as targetOrigin,
  Xu as themeParams,
  Aa as themeParamsAccentTextColor,
  es as themeParamsBackgroundColor,
  va as themeParamsBottomBarBgColor,
  ht as themeParamsButtonColor,
  ts as themeParamsButtonTextColor,
  ya as themeParamsDestructiveTextColor,
  Ba as themeParamsHeaderBackgroundColor,
  Pa as themeParamsHintColor,
  ka as themeParamsLinkColor,
  oi as themeParamsMountError,
  us as themeParamsMountPromise,
  os as themeParamsSecondaryBackgroundColor,
  Oa as themeParamsSectionBackgroundColor,
  Ia as themeParamsSectionHeaderTextColor,
  Va as themeParamsSectionSeparatorColor,
  K3 as themeParamsState,
  qa as themeParamsSubtitleTextColor,
  xa as themeParamsTextColor,
  ge as toRGB,
  ce as transformQueryUsing,
  Fn as unmountBackButton,
  fr as unmountBiometry,
  wr as unmountClosingBehavior,
  $a as unmountLocationManager,
  Ya as unmountMainButton,
  wi as unmountMiniApp,
  nc as unmountSecondaryButton,
  mc as unmountSettingsButton,
  Cc as unmountSwipeBehavior,
  ni as unmountThemeParams,
  Vc as unmountViewport,
  hr as updateBiometryToken,
  nl as viewport,
  Is as viewportContentSafeAreaInsetBottom,
  Vs as viewportContentSafeAreaInsetLeft,
  qs as viewportContentSafeAreaInsetRight,
  xs as viewportContentSafeAreaInsetTop,
  zt as viewportContentSafeAreaInsets,
  Ut as viewportHeight,
  Oc as viewportMountError,
  Ws as viewportMountPromise,
  Ns as viewportSafeAreaInsetBottom,
  Ds as viewportSafeAreaInsetLeft,
  Hs as viewportSafeAreaInsetRight,
  Ls as viewportSafeAreaInsetTop,
  Wt as viewportSafeAreaInsets,
  Gt as viewportStableHeight,
  ks as viewportState,
  Os as viewportWidth,
  p3 as wrapSafe
};
//# sourceMappingURL=@telegram-apps_sdk.js.map
