{"version": 3, "sources": ["../../src/error/TransactionNotStartedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,0BAA2B,SAAQ,2BAAY;IACxD;QACI,KAAK,CACD,yFAAyF,CAC5F,CAAA;IACL,CAAC;CACJ;AAND,gEAMC", "file": "TransactionNotStartedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when transaction is not started yet and user tries to run commit or rollback.\n */\nexport class TransactionNotStartedError extends TypeORMError {\n    constructor() {\n        super(\n            `Transaction is not started yet, start transaction before committing or rolling it back.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}