{"version": 3, "sources": ["../../src/driver/sqlserver/SqlServerConnectionCredentialsOptions.ts"], "names": [], "mappings": "", "file": "SqlServerConnectionCredentialsOptions.js", "sourcesContent": ["import { DefaultAuthentication } from \"./authentication/DefaultAuthentication\"\nimport { AzureActiveDirectoryAccessTokenAuthentication } from \"./authentication/AzureActiveDirectoryAccessTokenAuthentication\"\nimport { AzureActiveDirectoryDefaultAuthentication } from \"./authentication/AzureActiveDirectoryDefaultAuthentication\"\nimport { AzureActiveDirectoryMsiAppServiceAuthentication } from \"./authentication/AzureActiveDirectoryMsiAppServiceAuthentication\"\nimport { AzureActiveDirectoryMsiVmAuthentication } from \"./authentication/AzureActiveDirectoryMsiVmAuthentication\"\nimport { AzureActiveDirectoryPasswordAuthentication } from \"./authentication/AzureActiveDirectoryPasswordAuthentication\"\nimport { AzureActiveDirectoryServicePrincipalSecret } from \"./authentication/AzureActiveDirectoryServicePrincipalSecret\"\nimport { NtlmAuthentication } from \"./authentication/NtlmAuthentication\"\n\nexport type SqlServerConnectionCredentialsAuthenticationOptions =\n    | DefaultAuthentication\n    | NtlmAuthentication\n    | AzureActiveDirectoryAccessTokenAuthentication\n    | AzureActiveDirectoryDefaultAuthentication\n    | AzureActiveDirectoryMsiAppServiceAuthentication\n    | AzureActiveDirectoryMsiVmAuthentication\n    | AzureActiveDirectoryPasswordAuthentication\n    | AzureActiveDirectoryServicePrincipalSecret\n\n/**\n * SqlServer specific connection credential options.\n */\nexport interface SqlServerConnectionCredentialsOptions {\n    /**\n     * Connection url where the connection is performed.\n     */\n    readonly url?: string\n\n    /**\n     * Database host.\n     */\n    readonly host?: string\n\n    /**\n     * Database host port.\n     */\n    readonly port?: number\n\n    /**\n     * Database name to connect to.\n     */\n    readonly database?: string\n\n    /**\n     * Database username.\n     */\n    readonly username?: string\n\n    /**\n     * Database password.\n     */\n    readonly password?: string\n\n    /**\n     * Authentication settings\n     * It overrides username and password, when passed.\n     */\n    readonly authentication?: SqlServerConnectionCredentialsAuthenticationOptions\n\n    /**\n     * Once you set domain, driver will connect to SQL Server using domain login.\n     * @see SqlServerConnectionCredentialsOptions.authentication\n     * @see NtlmAuthentication\n     * @deprecated\n     */\n    readonly domain?: string\n}\n"], "sourceRoot": "../.."}