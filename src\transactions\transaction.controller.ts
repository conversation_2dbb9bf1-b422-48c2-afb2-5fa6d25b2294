import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { TransactionKind } from './transaction.entity';

@Controller('transactions')
export class TransactionController {
  constructor(private readonly transactionService: TransactionService) {}

  @Post('create')
  async createTransaction(@Body() body: {
    user_id: string;
    kind: TransactionKind;
    amount_ton: number;
    ref?: string;
    game_type?: string;
    game_data?: any;
  }) {
    if (!body.user_id || !body.kind || body.amount_ton === undefined) {
      throw new BadRequestException('Missing required fields');
    }
    return this.transactionService.createTransaction(
      body.user_id,
      body.kind,
      body.amount_ton,
      body.ref,
      body.game_type,
      body.game_data,
    );
  }

  @Get('user/:user_id')
  async getUserTransactions(
    @Param('user_id') user_id: string,
    @Query('limit') limit?: string,
  ) {
    const limitNum = limit ? parseInt(limit) : 50;
    return this.transactionService.getUserTransactions(user_id, limitNum);
  }

  @Get('all')
  async getAllTransactions(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit) : 100;
    return this.transactionService.getAllTransactions(limitNum);
  }

  @Get('kind/:kind')
  async getTransactionsByKind(
    @Param('kind') kind: TransactionKind,
    @Query('limit') limit?: string,
  ) {
    const limitNum = limit ? parseInt(limit) : 50;
    return this.transactionService.getTransactionsByKind(kind, limitNum);
  }

  @Get('game/:game_type')
  async getTransactionsByGame(
    @Param('game_type') game_type: string,
    @Query('limit') limit?: string,
  ) {
    const limitNum = limit ? parseInt(limit) : 50;
    return this.transactionService.getTransactionsByGame(game_type, limitNum);
  }

  @Get('stats/:user_id')
  async getUserGameStats(@Param('user_id') user_id: string) {
    return this.transactionService.getUserGameStats(user_id);
  }
}
