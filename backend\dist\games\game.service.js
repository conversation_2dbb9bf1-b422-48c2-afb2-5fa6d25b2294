"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameService = void 0;
const common_1 = require("@nestjs/common");
const user_service_1 = require("../users/user.service");
const transaction_service_1 = require("../transactions/transaction.service");
const transaction_entity_1 = require("../transactions/transaction.entity");
const config_service_1 = require("../config/config.service");
const crypto = require("crypto");
let GameService = class GameService {
    userService;
    transactionService;
    configService;
    constructor(userService, transactionService, configService) {
        this.userService = userService;
        this.transactionService = transactionService;
        this.configService = configService;
    }
    async getWinModifier() {
        const modifier = await this.configService.getConfig('win_modifier');
        return modifier ? parseFloat(modifier) : 0;
    }
    generateSeed() {
        return crypto.randomBytes(32).toString('hex');
    }
    generateRandomNumber(seed, min = 0, max = 1) {
        const hash = crypto.createHash('sha256').update(seed).digest('hex');
        const num = parseInt(hash.substring(0, 8), 16) / 0xffffffff;
        return min + (max - min) * num;
    }
    async placeBet(user_id, amount, game_type, game_data) {
        const user = await this.userService.getUser(user_id);
        if (!user || user.is_banned) {
            return false;
        }
        if (Number(user.balance_ton) < amount) {
            return false;
        }
        const success = await this.userService.deductBalance(user_id, amount);
        if (success) {
            await this.transactionService.createTransaction(user_id, transaction_entity_1.TransactionKind.GAME_BET, amount, undefined, game_type, game_data);
        }
        return success;
    }
    async payoutWin(user_id, amount, game_type, game_data) {
        await this.userService.updateBalance(user_id, amount);
        await this.transactionService.createTransaction(user_id, transaction_entity_1.TransactionKind.GAME_WIN, amount, undefined, game_type, game_data);
    }
    async playCrash(user_id, bet_amount, target_multiplier) {
        const betPlaced = await this.placeBet(user_id, bet_amount, 'crash', { target_multiplier });
        if (!betPlaced) {
            return { success: false };
        }
        const seed = this.generateSeed();
        const winModifier = await this.getWinModifier();
        const baseRandom = this.generateRandomNumber(seed);
        const adjustedRandom = Math.max(0.01, Math.min(0.99, baseRandom + winModifier));
        const crash_point = 1 / (1 - adjustedRandom) * 0.99;
        if (target_multiplier <= crash_point) {
            const payout = bet_amount * target_multiplier;
            await this.payoutWin(user_id, payout, 'crash', {
                target_multiplier,
                crash_point,
                payout,
                seed
            });
            return { success: true, crash_point, payout, seed };
        }
        return { success: true, crash_point, payout: 0, seed };
    }
    async playDouble(user_id, bet_amount, chosen_multiplier) {
        const validMultipliers = [2, 3, 5, 20];
        if (!validMultipliers.includes(chosen_multiplier)) {
            return { success: false };
        }
        const betPlaced = await this.placeBet(user_id, bet_amount, 'double', { chosen_multiplier });
        if (!betPlaced) {
            return { success: false };
        }
        const seed = this.generateSeed();
        const winModifier = await this.getWinModifier();
        const random = this.generateRandomNumber(seed) + winModifier * 0.1;
        let result_multiplier;
        if (random < 0.45) {
            result_multiplier = 2;
        }
        else if (random < 0.75) {
            result_multiplier = 3;
        }
        else if (random < 0.95) {
            result_multiplier = 5;
        }
        else {
            result_multiplier = 20;
        }
        if (chosen_multiplier === result_multiplier) {
            const payout = bet_amount * chosen_multiplier;
            await this.payoutWin(user_id, payout, 'double', {
                chosen_multiplier,
                result_multiplier,
                payout,
                seed,
            });
            return { success: true, result_multiplier, payout, seed };
        }
        return { success: true, result_multiplier, payout: 0, seed };
    }
};
exports.GameService = GameService;
exports.GameService = GameService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [user_service_1.UserService,
        transaction_service_1.TransactionService,
        config_service_1.ConfigService])
], GameService);
//# sourceMappingURL=game.service.js.map