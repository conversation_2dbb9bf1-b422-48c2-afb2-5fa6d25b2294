import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faGift, 
  faUser, 
  faCoins,
  faCheckCircle,
  faInfoCircle,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import ApiService from '../services/ApiService';
import './Withdraw.css';

const Withdraw = ({ user, balance, updateBalance }) => {
  const [availableGifts, setAvailableGifts] = useState([]);
  const [selectedGift, setSelectedGift] = useState(null);
  const [recipientUsername, setRecipientUsername] = useState('');
  const [loading, setLoading] = useState(true);
  const [withdrawing, setWithdrawing] = useState(false);
  const [withdrawResult, setWithdrawResult] = useState(null);

  useEffect(() => {
    loadAvailableGifts();
  }, []);

  const loadAvailableGifts = async () => {
    try {
      const prices = await ApiService.getAllPrices();
      // Filter gifts that user can afford
      const affordable = prices.filter(gift => parseFloat(gift.min_ton) <= balance);
      setAvailableGifts(affordable);
    } catch (error) {
      console.error('Error loading gifts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGiftSelect = (gift) => {
    setSelectedGift(gift);
    setWithdrawResult(null);
  };

  const handleWithdraw = async () => {
    if (!selectedGift) {
      alert('Выберите подарок для вывода');
      return;
    }

    if (!recipientUsername.trim()) {
      alert('Введите username получателя');
      return;
    }

    if (parseFloat(selectedGift.min_ton) > balance) {
      alert('Недостаточно средств');
      return;
    }

    setWithdrawing(true);
    setWithdrawResult(null);

    try {
      // In real implementation, this would call withdraw API
      // For demo, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate successful withdrawal
      const newBalance = balance - parseFloat(selectedGift.min_ton);
      updateBalance(newBalance);

      setWithdrawResult({
        success: true,
        gift: selectedGift,
        recipient: recipientUsername,
        amount: selectedGift.min_ton,
      });

      // Reset form
      setSelectedGift(null);
      setRecipientUsername('');
      
    } catch (error) {
      console.error('Error withdrawing:', error);
      setWithdrawResult({
        success: false,
        error: 'Ошибка при выводе средств',
      });
    } finally {
      setWithdrawing(false);
    }
  };

  if (loading) {
    return (
      <div className="withdraw-loading">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <FontAwesomeIcon icon={faSpinner} size="2x" className="text-gold" />
        </motion.div>
        <p>Загрузка доступных подарков...</p>
      </div>
    );
  }

  return (
    <div className="withdraw">
      {/* Header */}
      <motion.div
        className="withdraw-header"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1>
          <FontAwesomeIcon icon={faGift} className="text-gold" />
          Вывод средств
        </h1>
        <p>Выберите подарок и получателя</p>
        <div className="balance-display">
          <FontAwesomeIcon icon={faCoins} className="text-green" />
          <span>Доступно: {balance.toFixed(2)} TON</span>
        </div>
      </motion.div>

      {/* Gift Selection */}
      <motion.div
        className="gift-selection glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <h2>Выберите подарок</h2>
        {availableGifts.length === 0 ? (
          <div className="no-gifts">
            <FontAwesomeIcon icon={faInfoCircle} className="text-gold" />
            <p>Недостаточно средств для вывода подарков</p>
            <p className="min-amount">Минимальная сумма для вывода: 0.5 TON</p>
          </div>
        ) : (
          <div className="gifts-grid">
            {availableGifts.map((gift) => (
              <motion.div
                key={gift.model_key}
                className={`gift-card ${selectedGift?.model_key === gift.model_key ? 'selected' : ''}`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleGiftSelect(gift)}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="gift-image">
                  <img src={gift.image_url} alt={gift.model_key} />
                </div>
                <div className="gift-info">
                  <h3>{gift.model_key}</h3>
                  <div className="gift-price">
                    <FontAwesomeIcon icon={faCoins} className="text-green" />
                    <span>{parseFloat(gift.min_ton).toFixed(2)} TON</span>
                  </div>
                </div>
                {selectedGift?.model_key === gift.model_key && (
                  <div className="selected-indicator">
                    <FontAwesomeIcon icon={faCheckCircle} />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Recipient Input */}
      {selectedGift && (
        <motion.div
          className="recipient-section glass"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h2>Получатель</h2>
          <div className="input-group">
            <div className="input-wrapper">
              <FontAwesomeIcon icon={faUser} className="input-icon" />
              <input
                type="text"
                value={recipientUsername}
                onChange={(e) => setRecipientUsername(e.target.value)}
                placeholder="@username получателя"
                className="input"
                disabled={withdrawing}
              />
            </div>
          </div>
          
          <div className="withdraw-summary">
            <div className="summary-item">
              <span>Подарок:</span>
              <span>{selectedGift.model_key}</span>
            </div>
            <div className="summary-item">
              <span>Стоимость:</span>
              <span className="text-green">{parseFloat(selectedGift.min_ton).toFixed(2)} TON</span>
            </div>
            <div className="summary-item">
              <span>Получатель:</span>
              <span>{recipientUsername || 'Не указан'}</span>
            </div>
          </div>

          <button
            className="btn btn-primary withdraw-button"
            onClick={handleWithdraw}
            disabled={!recipientUsername.trim() || withdrawing}
          >
            {withdrawing ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="fa-spin" />
                Отправка...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faGift} />
                Отправить подарок
              </>
            )}
          </button>
        </motion.div>
      )}

      {/* Withdraw Result */}
      {withdrawResult && (
        <motion.div
          className={`withdraw-result glass ${withdrawResult.success ? 'success' : 'error'}`}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="result-icon">
            {withdrawResult.success ? (
              <FontAwesomeIcon icon={faCheckCircle} className="text-green" />
            ) : (
              <FontAwesomeIcon icon={faInfoCircle} className="text-danger" />
            )}
          </div>
          <div className="result-content">
            {withdrawResult.success ? (
              <>
                <h3>Подарок отправлен!</h3>
                <p>
                  Подарок "{withdrawResult.gift.model_key}" успешно отправлен 
                  пользователю {withdrawResult.recipient}
                </p>
                <p className="amount-deducted">
                  Списано: {parseFloat(withdrawResult.amount).toFixed(2)} TON
                </p>
              </>
            ) : (
              <>
                <h3>Ошибка</h3>
                <p>{withdrawResult.error}</p>
              </>
            )}
          </div>
        </motion.div>
      )}

      {/* Info */}
      <motion.div
        className="withdraw-info glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <FontAwesomeIcon icon={faInfoCircle} className="text-gold" />
        <div className="info-content">
          <h3>Как работает вывод</h3>
          <ul>
            <li>Выберите подарок из доступных</li>
            <li>Укажите @username получателя в Telegram</li>
            <li>Подарок будет отправлен через бота @pepecas_receiver</li>
            <li>Средства спишутся с вашего баланса</li>
          </ul>
        </div>
      </motion.div>
    </div>
  );
};

export default Withdraw;
