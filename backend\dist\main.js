"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const config_service_1 = require("./config/config.service");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.enableCors({
        origin: ['http://localhost:5173', 'https://*.twc1.net', 'https://*.vercel.app'],
        methods: 'GET,POST,PUT,DELETE,PATCH',
        allowedHeaders: 'Content-Type,Authorization',
        credentials: true,
    });
    const configService = app.get(config_service_1.ConfigService);
    await configService.initializeDefaults();
    await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
//# sourceMappingURL=main.js.map