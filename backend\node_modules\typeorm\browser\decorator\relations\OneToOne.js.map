{"version": 3, "sources": ["../browser/src/decorator/relations/OneToOne.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAItD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAA;AAqBpD;;;GAGG;AACH,MAAM,UAAU,QAAQ,CACpB,oBAA8D,EAC9D,oBAAsE,EACtE,OAAyB;IAEzB,uBAAuB;IACvB,IAAI,mBAAkD,CAAA;IACtD,IAAI,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;QAC7C,OAAO,GAAoB,oBAAoB,CAAA;IACnD,CAAC;SAAM,CAAC;QACJ,mBAAmB,GAAG,oBAA2B,CAAA;IACrD,CAAC;IAED,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAqB,CAAA;QAE7C,4CAA4C;QAC5C,IAAI,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAC5D,IAAI,CAAC,MAAM,IAAI,OAAO,IAAK,OAAe,CAAC,WAAW,EAAE,CAAC;YACrD,0BAA0B;YAC1B,MAAM,aAAa,GAAI,OAAe,CAAC,WAAW,CAC9C,aAAa,EACb,MAAM,EACN,YAAY,CACf,CAAA;YACD,IACI,aAAa;gBACb,OAAO,aAAa,CAAC,IAAI,KAAK,QAAQ;gBACtC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS;gBAE9C,MAAM,GAAG,IAAI,CAAA;QACrB,CAAC;QAED,sBAAsB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;YACpC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,+BAA+B;YAC/B,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,oBAAoB;YAC1B,mBAAmB,EAAE,mBAAmB;YACxC,OAAO,EAAE,OAAO;SACK,CAAC,CAAA;IAC9B,CAAC,CAAA;AACL,CAAC", "file": "OneToOne.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { RelationMetadataArgs } from \"../../metadata-args/RelationMetadataArgs\"\nimport { ObjectType } from \"../../common/ObjectType\"\nimport { RelationOptions } from \"../options/RelationOptions\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\n\n/**\n * One-to-one relation allows the creation of a direct relation between two entities. Entity1 has only one Entity2.\n * Entity1 is the owner of the relationship, and stores Entity2 id on its own side.\n */\nexport function OneToOne<T>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    options?: RelationOptions,\n): PropertyDecorator\n\n/**\n * One-to-one relation allows the creation of a direct relation between two entities. Entity1 has only one Entity2.\n * Entity1 is the owner of the relationship, and stores Entity2 id on its own side.\n */\nexport function OneToOne<T>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    inverseSide?: string | ((object: T) => any),\n    options?: RelationOptions,\n): PropertyDecorator\n\n/**\n * One-to-one relation allows the creation of a direct relation between two entities. Entity1 has only one Entity2.\n * Entity1 is the owner of the relationship, and stores Entity2 id on its own side.\n */\nexport function OneToOne<T>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    inverseSideOrOptions?: string | ((object: T) => any) | RelationOptions,\n    options?: RelationOptions,\n): PropertyDecorator {\n    // normalize parameters\n    let inverseSideProperty: string | ((object: T) => any)\n    if (ObjectUtils.isObject(inverseSideOrOptions)) {\n        options = <RelationOptions>inverseSideOrOptions\n    } else {\n        inverseSideProperty = inverseSideOrOptions as any\n    }\n\n    return function (object: Object, propertyName: string) {\n        if (!options) options = {} as RelationOptions\n\n        // now try to determine it its lazy relation\n        let isLazy = options && options.lazy === true ? true : false\n        if (!isLazy && Reflect && (Reflect as any).getMetadata) {\n            // automatic determination\n            const reflectedType = (Reflect as any).getMetadata(\n                \"design:type\",\n                object,\n                propertyName,\n            )\n            if (\n                reflectedType &&\n                typeof reflectedType.name === \"string\" &&\n                reflectedType.name.toLowerCase() === \"promise\"\n            )\n                isLazy = true\n        }\n\n        getMetadataArgsStorage().relations.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            // propertyType: reflectedType,\n            isLazy: isLazy,\n            relationType: \"one-to-one\",\n            type: typeFunctionOrTarget,\n            inverseSideProperty: inverseSideProperty,\n            options: options,\n        } as RelationMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}