{"version": 3, "sources": ["../browser/src/error/OptimisticLockCanNotBeUsedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,+BAAgC,SAAQ,YAAY;IAC7D;QACI,KAAK,CAAC,4DAA4D,CAAC,CAAA;IACvE,CAAC;CACJ", "file": "OptimisticLockCanNotBeUsedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when an optimistic lock cannot be used in query builder.\n */\nexport class OptimisticLockCanNotBeUsedError extends TypeORMError {\n    constructor() {\n        super(`The optimistic lock can be used only with getOne() method.`)\n    }\n}\n"], "sourceRoot": ".."}