{"version": 3, "sources": ["../browser/src/decorator/relations/RelationId.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAItD;;;;GAIG;AACH,MAAM,UAAU,UAAU,CACtB,QAAuC,EACvC,KAAc,EACd,mBAE4B;IAE5B,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,sBAAsB,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC;YACtC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE,KAAK;YACZ,mBAAmB,EAAE,mBAAmB;SACjB,CAAC,CAAA;IAChC,CAAC,CAAA;AACL,CAAC", "file": "RelationId.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { RelationIdMetadataArgs } from \"../../metadata-args/RelationIdMetadataArgs\"\nimport { SelectQueryBuilder } from \"../../query-builder/SelectQueryBuilder\"\n\n/**\n * Special decorator used to extract relation id into separate entity property.\n *\n * @experimental\n */\nexport function RelationId<T>(\n    relation: string | ((object: T) => any),\n    alias?: string,\n    queryBuilderFactory?: (\n        qb: SelectQueryBuilder<any>,\n    ) => SelectQueryBuilder<any>,\n): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().relationIds.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            relation: relation,\n            alias: alias,\n            queryBuilderFactory: queryBuilderFactory,\n        } as RelationIdMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}