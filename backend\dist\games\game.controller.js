"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameController = void 0;
const common_1 = require("@nestjs/common");
const game_service_1 = require("./game.service");
const coinflip_service_1 = require("./coinflip.service");
let GameController = class GameController {
    gameService;
    coinflipService;
    constructor(gameService, coinflipService) {
        this.gameService = gameService;
        this.coinflipService = coinflipService;
    }
    async playCrash(body) {
        if (!body.user_id || body.bet_amount === undefined || body.target_multiplier === undefined) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        if (body.bet_amount <= 0 || body.target_multiplier < 1) {
            throw new common_1.BadRequestException('Invalid bet amount or multiplier');
        }
        return this.gameService.playCrash(body.user_id, body.bet_amount, body.target_multiplier);
    }
    async playDouble(body) {
        if (!body.user_id || body.bet_amount === undefined || body.chosen_multiplier === undefined) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        if (body.bet_amount <= 0) {
            throw new common_1.BadRequestException('Invalid bet amount');
        }
        return this.gameService.playDouble(body.user_id, body.bet_amount, body.chosen_multiplier);
    }
    async createCoinflipRoom(body) {
        if (!body.user_id || body.stake === undefined) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        if (body.stake <= 0) {
            throw new common_1.BadRequestException('Invalid stake amount');
        }
        const room = await this.coinflipService.createRoom(body.user_id, body.stake);
        if (!room) {
            throw new common_1.BadRequestException('Unable to create room - insufficient balance or user banned');
        }
        return room;
    }
    async joinCoinflipRoom(body) {
        if (!body.room_id || !body.user_id) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        const result = await this.coinflipService.joinRoom(body.room_id, body.user_id);
        if (!result.success) {
            throw new common_1.BadRequestException('Unable to join room - room not available, insufficient balance, or user banned');
        }
        return result;
    }
    async getWaitingRooms() {
        return this.coinflipService.getWaitingRooms();
    }
    async getRecentGames() {
        return this.coinflipService.getRecentGames();
    }
    async getUserRooms(user_id) {
        return this.coinflipService.getUserRooms(user_id);
    }
    async getRoomById(room_id) {
        const roomIdNum = parseInt(room_id);
        if (isNaN(roomIdNum)) {
            throw new common_1.BadRequestException('Invalid room ID');
        }
        const room = await this.coinflipService.getRoomById(roomIdNum);
        if (!room) {
            throw new common_1.NotFoundException('Room not found');
        }
        return room;
    }
};
exports.GameController = GameController;
__decorate([
    (0, common_1.Post)('crash/play'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GameController.prototype, "playCrash", null);
__decorate([
    (0, common_1.Post)('double/play'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GameController.prototype, "playDouble", null);
__decorate([
    (0, common_1.Post)('coinflip/create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GameController.prototype, "createCoinflipRoom", null);
__decorate([
    (0, common_1.Post)('coinflip/join'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GameController.prototype, "joinCoinflipRoom", null);
__decorate([
    (0, common_1.Get)('coinflip/waiting'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], GameController.prototype, "getWaitingRooms", null);
__decorate([
    (0, common_1.Get)('coinflip/recent'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], GameController.prototype, "getRecentGames", null);
__decorate([
    (0, common_1.Get)('coinflip/user/:user_id'),
    __param(0, (0, common_1.Param)('user_id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GameController.prototype, "getUserRooms", null);
__decorate([
    (0, common_1.Get)('coinflip/room/:room_id'),
    __param(0, (0, common_1.Param)('room_id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GameController.prototype, "getRoomById", null);
exports.GameController = GameController = __decorate([
    (0, common_1.Controller)('games'),
    __metadata("design:paramtypes", [game_service_1.GameService,
        coinflip_service_1.CoinflipService])
], GameController);
//# sourceMappingURL=game.controller.js.map