# 🚀 Деплой PEPE CAS

## Frontend (Vercel)

### 1. Подготовка
```bash
cd frontend
npm run build
```

### 2. Деплой на Vercel
```bash
npm i -g vercel
vercel --prod
```

### 3. Настройка переменных
- `VITE_API_URL` - URL вашего backend API

## Backend (Railway)

### 1. Подготовка
```bash
cd backend
npm run build
```

### 2. Создание railway.json
```json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "healthcheckPath": "/",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### 3. Деплой
```bash
npm i -g @railway/cli
railway login
railway init
railway up
```

## База данных (PostgreSQL)

### 1. Обновление конфигурации
```typescript
// backend/src/ormconfig.ts
export const typeOrmConfig: TypeOrmModuleOptions = {
  type: 'postgres',
  url: process.env.DATABASE_URL,
  entities: [__dirname + '/**/*.entity{.ts,.js}'],
  synchronize: process.env.NODE_ENV !== 'production',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};
```

### 2. Установка драйвера
```bash
npm install pg @types/pg
npm uninstall sqlite3
```

## Telegram Mini App

### 1. Настройка в BotFather
1. Создать бота: `/newbot`
2. Получить токен
3. Создать Mini App: `/newapp`
4. Указать URL: `https://your-frontend-domain.vercel.app`

### 2. Настройка Web App
```javascript
// frontend/src/main.jsx
const TELEGRAM_BOT_TOKEN = '**********:AAHrRX3Z1tLtO7aE_gPblArhJMGTv5kixvI';
```

### 3. Настройка домена
- Добавить домен в настройки бота
- Настроить HTTPS сертификат
- Проверить CORS настройки

## Переменные окружения

### Backend (.env)
```env
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://user:password@host:port/database
TELEGRAM_BOT_TOKEN=**********:AAHrRX3Z1tLtO7aE_gPblArhJMGTv5kixvI
TELEGRAM_API_ID=13434740
TELEGRAM_API_HASH=ddcb8777d24aa5a651a5bab7e17c585d
```

### Frontend (.env)
```env
VITE_API_URL=https://your-backend-domain.railway.app
VITE_TELEGRAM_BOT_TOKEN=**********:AAHrRX3Z1tLtO7aE_gPblArhJMGTv5kixvI
```

## Мониторинг

### 1. Логирование
```typescript
// backend/src/main.ts
import { Logger } from '@nestjs/common';

const logger = new Logger('Bootstrap');
logger.log(`Application is running on: ${await app.getUrl()}`);
```

### 2. Health Check
```typescript
// backend/src/health/health.controller.ts
@Get('health')
healthCheck() {
  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  };
}
```

## Безопасность

### 1. CORS
```typescript
// backend/src/main.ts
app.enableCors({
  origin: [
    'https://your-frontend-domain.vercel.app',
    'https://web.telegram.org',
  ],
  credentials: true,
});
```

### 2. Rate Limiting
```bash
npm install @nestjs/throttler
```

```typescript
// backend/src/app.module.ts
import { ThrottlerModule } from '@nestjs/throttler';

@Module({
  imports: [
    ThrottlerModule.forRoot({
      ttl: 60,
      limit: 100,
    }),
  ],
})
```

### 3. Helmet
```bash
npm install helmet
```

```typescript
// backend/src/main.ts
import helmet from 'helmet';
app.use(helmet());
```

## Backup

### 1. База данных
```bash
# Создание backup
pg_dump $DATABASE_URL > backup.sql

# Восстановление
psql $DATABASE_URL < backup.sql
```

### 2. Автоматический backup
```yaml
# .github/workflows/backup.yml
name: Database Backup
on:
  schedule:
    - cron: '0 2 * * *'  # Каждый день в 2:00
jobs:
  backup:
    runs-on: ubuntu-latest
    steps:
      - name: Backup Database
        run: |
          pg_dump ${{ secrets.DATABASE_URL }} > backup-$(date +%Y%m%d).sql
```

## Масштабирование

### 1. Redis для кеширования
```bash
npm install redis @nestjs/redis
```

### 2. WebSocket для реального времени
```bash
npm install @nestjs/websockets @nestjs/platform-socket.io
```

### 3. Очереди для фоновых задач
```bash
npm install @nestjs/bull bull
```

## Мониторинг производительности

### 1. Prometheus метрики
```bash
npm install prom-client
```

### 2. APM мониторинг
```bash
npm install @sentry/node
```

## Тестирование в продакшене

### 1. Smoke тесты
```bash
curl https://your-backend-domain.railway.app/health
curl https://your-backend-domain.railway.app/users/leaderboard
```

### 2. Load тестирование
```bash
npm install -g artillery
artillery quick --count 10 --num 100 https://your-backend-domain.railway.app
```

## Rollback план

### 1. Откат Frontend
```bash
vercel rollback
```

### 2. Откат Backend
```bash
railway rollback
```

### 3. Откат базы данных
```bash
psql $DATABASE_URL < backup-previous.sql
```

---

**Готово!** 🎉 Ваше казино PEPE CAS теперь доступно в продакшене!
