import { User } from '../users/user.entity';
export declare enum CoinflipStatus {
    WAITING = "WAITING",
    PLAYING = "PLAYING",
    FINISHED = "FINISHED"
}
export declare class CoinflipRoom {
    id: number;
    stake: number;
    playerA: User;
    playerA_id: string;
    playerB: User;
    playerB_id: string;
    winner: User;
    winner_id: string;
    status: CoinflipStatus;
    result: string;
    seed: string;
    created_at: Date;
    updated_at: Date;
}
