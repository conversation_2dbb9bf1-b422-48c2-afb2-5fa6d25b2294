import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Rocket,
  Coins,
  Dice6,
  Trophy,
  Flame,
  Users,
  TrendingUp,
  Loader2
} from 'lucide-react';
import ApiService from '../services/ApiService';
import './Home.css';

const Home = ({ user, balance, updateBalance }) => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [recentGames, setRecentGames] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHomeData();
  }, [user]);

  const loadHomeData = async () => {
    try {
      const [leaderboardData, recentGamesData, userStats] = await Promise.all([
        ApiService.getLeaderboard(),
        ApiService.getRecentGames(),
        user ? ApiService.getUserGameStats(user.telegram_id) : null,
      ]);

      setLeaderboard(leaderboardData.slice(0, 5));
      setRecentGames(recentGamesData.slice(0, 3));
      setStats(userStats);
    } catch (error) {
      console.error('Error loading home data:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      id: 'crash',
      title: 'Crash',
      icon: Rocket,
      color: 'var(--primary-green)',
      description: 'Забери до краха!',
    },
    {
      id: 'coinflip',
      title: 'Coinflip',
      icon: Coins,
      color: 'var(--primary-orange)',
      description: 'PvP дуэли',
    },
    {
      id: 'double',
      title: 'Double',
      icon: Dice6,
      color: 'var(--primary-red)',
      description: 'Рулетка x20',
    },
  ];

  if (loading) {
    return (
      <div className="home-loading">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <Loader2 size={32} className="text-blue" />
        </motion.div>
        <p className="text-secondary">Загрузка данных...</p>
      </div>
    );
  }

  return (
    <div className="home">
      {/* Welcome Section */}
      <motion.div
        className="welcome-section card-elevated"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="welcome-content">
          <h1 className="text-large-title">Добро пожаловать, {user?.firstname || 'Игрок'}!</h1>
          <p className="welcome-subtitle text-callout text-secondary-label">
            Твой баланс: <span className="text-success font-semibold">{balance.toFixed(2)} TON</span>
          </p>
          <div className="welcome-stats">
            {stats && (
              <>
                <div className="stat-item">
                  <TrendingUp size={16} className="text-primary" />
                  <span className="text-subheadline">Игр: {stats.total_bets}</span>
                </div>
                <div className="stat-item">
                  <Trophy size={16} className="text-warning" />
                  <span className="text-subheadline">Побед: {stats.total_wins}</span>
                </div>
              </>
            )}
          </div>
        </div>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        className="quick-actions"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <h2 className="text-title-2 flex items-center gap-3">
          <Flame size={20} className="text-warning" />
          Популярные игры
        </h2>
        <div className="actions-grid">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.id}
              className="action-card card"
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="action-icon" style={{ color: action.color }}>
                <action.icon size={28} />
              </div>
              <h3 className="text-headline">{action.title}</h3>
              <p className="text-footnote text-secondary-label">{action.description}</p>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Leaderboard Preview */}
      <motion.div
        className="leaderboard-preview card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h2 className="text-title-2 flex items-center gap-3">
          <Trophy size={20} className="text-warning" />
          Топ игроков
        </h2>
        <div className="leaderboard-list">
          {leaderboard.map((player, index) => (
            <motion.div
              key={player.telegram_id}
              className="leaderboard-item"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="player-rank">
                <span className={`rank ${index < 3 ? 'top-three' : ''}`}>
                  {index + 1}
                </span>
              </div>
              <div className="player-info">
                <span className="player-name">{player.firstname}</span>
                <span className="player-balance text-green">
                  {parseFloat(player.balance_ton).toFixed(2)} TON
                </span>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Recent Games */}
      {recentGames.length > 0 && (
        <motion.div
          className="recent-games card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h2 className="text-title-2 flex items-center gap-3">
            <Users size={20} className="text-primary" />
            Последние игры
          </h2>
          <div className="games-list">
            {recentGames.map((game, index) => (
              <motion.div
                key={game.id}
                className="game-item"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="game-info">
                  <span className="game-type">Coinflip</span>
                  <span className="game-stake text-orange">
                    {parseFloat(game.stake).toFixed(2)} TON
                  </span>
                </div>
                <div className="game-result">
                  <span className={`result ${game.result}`}>
                    {game.result === 'heads' ? '🪙' : '🎯'}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default Home;
