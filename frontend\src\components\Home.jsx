import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Rocket,
  Coins,
  Dice6,
  Trophy,
  Flame,
  Users,
  TrendingUp,
  Loader2,
  Wallet,
  Target
} from 'lucide-react';
import ApiService from '../services/ApiService';
import './HomeNew.css';

const Home = ({ user, balance, updateBalance }) => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [recentGames, setRecentGames] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHomeData();
  }, [user]);

  const loadHomeData = async () => {
    try {
      const [leaderboardData, recentGamesData, userStats] = await Promise.all([
        ApiService.getLeaderboard(),
        ApiService.getRecentGames(),
        user ? ApiService.getUserGameStats(user.telegram_id) : null,
      ]);

      setLeaderboard(leaderboardData.slice(0, 5));
      setRecentGames(recentGamesData.slice(0, 3));
      setStats(userStats);
    } catch (error) {
      console.error('Error loading home data:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      id: 'crash',
      title: 'Crash',
      icon: Rocket,
      color: 'var(--primary-green)',
      description: 'Забери до краха!',
    },
    {
      id: 'coinflip',
      title: 'Coinflip',
      icon: Coins,
      color: 'var(--primary-orange)',
      description: 'PvP дуэли',
    },
    {
      id: 'double',
      title: 'Double',
      icon: Dice6,
      color: 'var(--primary-red)',
      description: 'Рулетка x20',
    },
  ];

  if (loading) {
    return (
      <div className="home-loading">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <Loader2 size={32} className="text-blue" />
        </motion.div>
        <p className="text-secondary">Загрузка данных...</p>
      </div>
    );
  }

  return (
    <div className="home">
      {/* Hero Section */}
      <motion.div
        className="hero-section card-elevated"
        initial={{ opacity: 0, y: 30, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{
          duration: 0.6,
          ease: [0.25, 0.46, 0.45, 0.94],
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
      >
        <div className="hero-background">
          <div className="hero-gradient"></div>
        </div>
        <div className="hero-content">
          <div className="welcome-header">
            <div className="welcome-icon">
              <Trophy size={32} className="text-warning" />
            </div>
            <div className="welcome-text">
              <h1 className="text-title-1">Добро пожаловать!</h1>
              <p className="text-callout text-secondary-label">{user?.firstname || 'Игрок'}</p>
            </div>
          </div>

          <div className="balance-card">
            <div className="balance-header">
              <Wallet size={20} className="text-success" />
              <span className="text-callout text-secondary-label">Ваш баланс</span>
            </div>
            <div className="balance-amount">
              <span className="text-large-title font-bold text-success">{balance.toFixed(2)}</span>
              <span className="text-title-3 text-secondary-label">TON</span>
            </div>
          </div>

          <div className="stats-grid">
            {stats && (
              <>
                <div className="stat-card">
                  <div className="stat-icon">
                    <TrendingUp size={20} className="text-primary" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-value text-title-3 font-semibold">{stats.total_bets}</span>
                    <span className="stat-label text-footnote text-tertiary-label">Всего игр</span>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">
                    <Trophy size={20} className="text-warning" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-value text-title-3 font-semibold">{stats.total_wins}</span>
                    <span className="stat-label text-footnote text-tertiary-label">Побед</span>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">
                    <Target size={20} className="text-danger" />
                  </div>
                  <div className="stat-content">
                    <span className="stat-value text-title-3 font-semibold">
                      {stats.total_bets > 0 ? Math.round((stats.total_wins / stats.total_bets) * 100) : 0}%
                    </span>
                    <span className="stat-label text-footnote text-tertiary-label">Винрейт</span>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </motion.div>

      {/* Games Section */}
      <motion.div
        className="games-section"
        initial={{ opacity: 0, y: 40, scale: 0.98 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{
          duration: 0.7,
          delay: 0.2,
          ease: [0.25, 0.46, 0.45, 0.94],
          type: "spring",
          stiffness: 280,
          damping: 25
        }}
      >
        <div className="section-header">
          <div className="section-title">
            <Flame size={24} className="text-warning" />
            <h2 className="text-title-2">Популярные игры</h2>
          </div>
          <div className="section-badge">
            <span className="text-footnote text-tertiary-label">3 игры</span>
          </div>
        </div>

        <div className="games-grid">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.id}
              className="game-card card-elevated"
              whileHover={{
                scale: 1.02,
                y: -6,
                transition: {
                  duration: 0.2,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }
              }}
              whileTap={{
                scale: 0.96,
                transition: {
                  duration: 0.1,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }
              }}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                duration: 0.5,
                delay: 0.3 + (index * 0.1),
                ease: [0.25, 0.46, 0.45, 0.94],
                type: "spring",
                stiffness: 350,
                damping: 30
              }}
            >
              <div className="game-header">
                <div className="game-icon" style={{ backgroundColor: `${action.color}20`, color: action.color }}>
                  <action.icon size={24} />
                </div>
                <div className="game-status">
                  <div className="status-dot"></div>
                  <span className="text-footnote text-tertiary-label">Онлайн</span>
                </div>
              </div>

              <div className="game-content">
                <h3 className="text-headline font-semibold">{action.title}</h3>
                <p className="text-footnote text-secondary-label">{action.description}</p>
              </div>

              <div className="game-footer">
                <div className="game-stats">
                  <div className="stat-mini">
                    <Users size={12} />
                    <span className="text-caption-1">24/7</span>
                  </div>
                  <div className="stat-mini">
                    <TrendingUp size={12} />
                    <span className="text-caption-1">Популярно</span>
                  </div>
                </div>
                <button className="play-btn">
                  <span className="text-footnote font-semibold">Играть</span>
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Leaderboard Section */}
      <motion.div
        className="leaderboard-section card"
        initial={{ opacity: 0, y: 50, scale: 0.97 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{
          duration: 0.8,
          delay: 0.4,
          ease: [0.25, 0.46, 0.45, 0.94],
          type: "spring",
          stiffness: 260,
          damping: 20
        }}
      >
        <div className="section-header">
          <div className="section-title">
            <Trophy size={24} className="text-warning" />
            <h2 className="text-title-2">Топ игроков</h2>
          </div>
          <div className="section-badge">
            <span className="text-footnote text-tertiary-label">Топ {leaderboard.length}</span>
          </div>
        </div>

        <div className="leaderboard-list">
          {leaderboard.map((player, index) => (
            <motion.div
              key={player.telegram_id}
              className="leaderboard-item"
              initial={{ opacity: 0, x: -30, scale: 0.95 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              transition={{
                duration: 0.4,
                delay: 0.5 + (index * 0.08),
                ease: [0.25, 0.46, 0.45, 0.94],
                type: "spring",
                stiffness: 400,
                damping: 35
              }}
            >
              <div className="player-rank">
                <div className={`rank-badge ${index < 3 ? 'top-three' : ''}`}>
                  {index < 3 ? (
                    <Trophy size={20} className={`rank-icon ${
                      index === 0 ? 'text-warning' :
                      index === 1 ? 'text-secondary-label' :
                      'text-orange'
                    }`} />
                  ) : (
                    <span className="rank-number text-footnote font-semibold">
                      {index + 1}
                    </span>
                  )}
                </div>
              </div>

              <div className="player-info">
                <div className="player-details">
                  <span className="player-name text-callout font-semibold">{player.firstname}</span>
                  <span className="player-status text-caption-1 text-tertiary-label">Активен</span>
                </div>
                <div className="player-balance">
                  <span className="balance-amount text-headline font-bold text-success">
                    {parseFloat(player.balance_ton).toFixed(2)}
                  </span>
                  <span className="balance-currency text-footnote text-secondary-label">TON</span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Recent Games */}
      {recentGames.length > 0 && (
        <motion.div
          className="recent-games card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h2 className="text-title-2 flex items-center gap-3">
            <Users size={20} className="text-primary" />
            Последние игры
          </h2>
          <div className="games-list">
            {recentGames.map((game, index) => (
              <motion.div
                key={game.id}
                className="game-item"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="game-info">
                  <span className="game-type">Coinflip</span>
                  <span className="game-stake text-orange">
                    {parseFloat(game.stake).toFixed(2)} TON
                  </span>
                </div>
                <div className="game-result">
                  <div className={`result-icon ${game.result}`}>
                    {game.result === 'heads' ? (
                      <Coins size={16} className="text-warning" />
                    ) : (
                      <Target size={16} className="text-danger" />
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default Home;
