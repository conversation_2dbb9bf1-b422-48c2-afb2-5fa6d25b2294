import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faRocket, 
  faCoins, 
  faDice, 
  faTrophy,
  faFire,
  faUsers,
  faChartLine
} from '@fortawesome/free-solid-svg-icons';
import ApiService from '../services/ApiService';
import './Home.css';

const Home = ({ user, balance, updateBalance }) => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [recentGames, setRecentGames] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHomeData();
  }, [user]);

  const loadHomeData = async () => {
    try {
      const [leaderboardData, recentGamesData, userStats] = await Promise.all([
        ApiService.getLeaderboard(),
        ApiService.getRecentGames(),
        user ? ApiService.getUserGameStats(user.telegram_id) : null,
      ]);

      setLeaderboard(leaderboardData.slice(0, 5));
      setRecentGames(recentGamesData.slice(0, 3));
      setStats(userStats);
    } catch (error) {
      console.error('Error loading home data:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      id: 'crash',
      title: 'Crash',
      icon: faRocket,
      color: 'var(--primary-green)',
      description: 'Забери до краха!',
    },
    {
      id: 'coinflip',
      title: 'Coinflip',
      icon: faCoins,
      color: 'var(--primary-gold)',
      description: 'PvP дуэли',
    },
    {
      id: 'double',
      title: 'Double',
      icon: faDice,
      color: 'var(--danger-red)',
      description: 'Рулетка x20',
    },
  ];

  if (loading) {
    return (
      <div className="home-loading">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <FontAwesomeIcon icon={faCoins} size="2x" className="text-gold" />
        </motion.div>
        <p>Загрузка данных...</p>
      </div>
    );
  }

  return (
    <div className="home">
      {/* Welcome Section */}
      <motion.div
        className="welcome-section glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="welcome-content">
          <h1>Добро пожаловать, {user?.firstname || 'Игрок'}!</h1>
          <p className="welcome-subtitle">
            Твой баланс: <span className="text-green">{balance.toFixed(2)} TON</span>
          </p>
          <div className="welcome-stats">
            {stats && (
              <>
                <div className="stat-item">
                  <FontAwesomeIcon icon={faChartLine} className="text-gold" />
                  <span>Игр: {stats.total_bets}</span>
                </div>
                <div className="stat-item">
                  <FontAwesomeIcon icon={faTrophy} className="text-green" />
                  <span>Побед: {stats.total_wins}</span>
                </div>
              </>
            )}
          </div>
        </div>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        className="quick-actions"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <h2>
          <FontAwesomeIcon icon={faFire} className="text-gold" />
          Популярные игры
        </h2>
        <div className="actions-grid">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.id}
              className="action-card glass"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="action-icon" style={{ color: action.color }}>
                <FontAwesomeIcon icon={action.icon} />
              </div>
              <h3>{action.title}</h3>
              <p>{action.description}</p>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Leaderboard Preview */}
      <motion.div
        className="leaderboard-preview glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h2>
          <FontAwesomeIcon icon={faTrophy} className="text-gold" />
          Топ игроков
        </h2>
        <div className="leaderboard-list">
          {leaderboard.map((player, index) => (
            <motion.div
              key={player.telegram_id}
              className="leaderboard-item"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="player-rank">
                <span className={`rank ${index < 3 ? 'top-three' : ''}`}>
                  {index + 1}
                </span>
              </div>
              <div className="player-info">
                <span className="player-name">{player.firstname}</span>
                <span className="player-balance text-green">
                  {parseFloat(player.balance_ton).toFixed(2)} TON
                </span>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Recent Games */}
      {recentGames.length > 0 && (
        <motion.div
          className="recent-games glass"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h2>
            <FontAwesomeIcon icon={faUsers} className="text-green" />
            Последние игры
          </h2>
          <div className="games-list">
            {recentGames.map((game, index) => (
              <motion.div
                key={game.id}
                className="game-item"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="game-info">
                  <span className="game-type">Coinflip</span>
                  <span className="game-stake text-gold">
                    {parseFloat(game.stake).toFixed(2)} TON
                  </span>
                </div>
                <div className="game-result">
                  <span className={`result ${game.result}`}>
                    {game.result === 'heads' ? '🪙' : '🎯'}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default Home;
