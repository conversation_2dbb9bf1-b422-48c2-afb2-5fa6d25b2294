/* Apple System Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
  /* Official Apple Typography Stack */
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', system-ui, sans-serif;
  line-height: 1.47058823529;
  font-weight: 400;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  font-variant-numeric: tabular-nums;

  color: #FFFFFF;
  background: #000000;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  /* Official Apple UIKit System Colors - Dark Mode */
  --system-blue: #007AFF;
  --system-brown: #AC8E68;
  --system-cyan: #40CBE0;
  --system-green: #30D158;
  --system-indigo: #5E5CE6;
  --system-mint: #63E6E2;
  --system-orange: #FF9F0A;
  --system-pink: #FF2D92;
  --system-purple: #BF5AF2;
  --system-red: #FF453A;
  --system-teal: #40CBE0;
  --system-yellow: #FFD60A;

  /* UIKit Label Colors */
  --label: rgba(255, 255, 255, 1);
  --secondary-label: rgba(235, 235, 245, 0.6);
  --tertiary-label: rgba(235, 235, 245, 0.3);
  --quaternary-label: rgba(235, 235, 245, 0.18);

  /* UIKit Fill Colors */
  --system-fill: rgba(120, 120, 128, 0.2);
  --secondary-system-fill: rgba(120, 120, 128, 0.16);
  --tertiary-system-fill: rgba(118, 118, 128, 0.12);
  --quaternary-system-fill: rgba(118, 118, 128, 0.08);

  /* Semantic Colors */
  --color-primary: var(--ios-blue);
  --color-success: var(--ios-green);
  --color-warning: var(--ios-orange);
  --color-danger: var(--ios-red);
  --color-info: var(--ios-teal);

  /* System Grays - True iOS Values */
  --gray-1: #8E8E93;
  --gray-2: #636366;
  --gray-3: #48484A;
  --gray-4: #3A3A3C;
  --gray-5: #2C2C2E;
  --gray-6: #1C1C1E;

  /* Official UIKit Background Colors - Dark Mode */
  --system-background: #141414;
  --secondary-system-background: #1C1C1E;
  --tertiary-system-background: #2C2C2E;

  /* UIKit Grouped Background Colors */
  --system-grouped-background: #141414;
  --secondary-system-grouped-background: #1C1C1E;
  --tertiary-system-grouped-background: #2C2C2E;

  /* Custom Surface Colors for Cards */
  --surface-primary: var(--secondary-system-background);
  --surface-secondary: var(--tertiary-system-background);
  --surface-tertiary: rgba(44, 44, 46, 1);
  --surface-elevated: rgba(28, 28, 30, 1);
  --surface-hover: rgba(58, 58, 60, 0.8);

  /* UIKit Separator Colors */
  --separator: rgba(84, 84, 88, 0.6);
  --opaque-separator: #38383A;

  /* Custom Border Colors */
  --border-primary: var(--separator);
  --border-secondary: rgba(84, 84, 88, 0.3);
  --border-tertiary: rgba(84, 84, 88, 0.15);

  /* Shadow System */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

  /* Blur Effects */
  --blur-sm: blur(4px);
  --blur-md: blur(8px);
  --blur-lg: blur(16px);
  --blur-xl: blur(24px);
  --blur-2xl: blur(40px);

  /* Spacing Scale - iOS Human Interface Guidelines */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;

  /* Border Radius Scale */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 9999px;

  /* Official Apple Text Styles - iOS 17 */
  --large-title: 34px;
  --title-1: 28px;
  --title-2: 22px;
  --title-3: 20px;
  --headline: 17px;
  --body: 17px;
  --callout: 16px;
  --subheadline: 15px;
  --footnote: 13px;
  --caption-1: 12px;
  --caption-2: 11px;

  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Letter Spacing - iOS Specific */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slower: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* iOS Specific Easing */
  --ease-ios: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-ios-in: cubic-bezier(0.42, 0, 1, 1);
  --ease-ios-out: cubic-bezier(0, 0, 0.58, 1);
  --ease-ios-in-out: cubic-bezier(0.42, 0, 0.58, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height for mobile */
  background: var(--system-background);
  overflow-x: hidden;
  position: relative;

  /* iOS-specific optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  /* Prevent bounce scrolling on iOS */
  overscroll-behavior: none;
}

#root {
  width: 100%;
  min-height: 100vh;
  min-height: 100dvh;
  position: relative;
}

/* Official UIKit Card System */
.card {
  background: var(--secondary-system-background);
  border: 0.5px solid var(--separator);
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.33px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

.card:hover {
  background: var(--tertiary-system-background);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.card:active {
  transform: scale(0.98);
  transition: all 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Elevated Card Variant */
.card-elevated {
  background: var(--tertiary-system-background);
  box-shadow: var(--shadow-md);
  border: 0.5px solid var(--opaque-separator);
}

.card-elevated:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Glass Card Variant */
.card-glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: var(--blur-2xl);
  -webkit-backdrop-filter: var(--blur-2xl);
  border: 0.33px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    var(--shadow-lg),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Official UIKit Button System */
.btn {
  /* Base Structure */
  border: none;
  border-radius: 22px;
  padding: 10px 20px;
  font-size: var(--callout);
  font-weight: 600;
  font-family: inherit;
  line-height: 1.31;
  letter-spacing: -0.32px;

  /* Layout */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  min-height: 44px; /* iOS minimum touch target */
  min-width: 44px;

  /* Interaction */
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;

  /* Transitions */
  transition: all var(--transition-base) var(--ease-ios);

  /* Accessibility */
  position: relative;
  overflow: hidden;

  /* Prevent text selection */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity var(--transition-fast) var(--ease-ios);
  pointer-events: none;
}

.btn:hover::before {
  opacity: 1;
}

.btn:active {
  transform: scale(0.96);
  transition: transform var(--transition-fast) var(--ease-ios-in);
}

.btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
}

/* Primary Button - UIKit Blue */
.btn-primary {
  background: var(--system-blue);
  color: #FFFFFF;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: #0056CC;
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-primary:active {
  background: #004999;
  box-shadow: var(--shadow-sm);
}

/* Secondary Button - Glass Effect */
.btn-secondary {
  background: var(--surface-secondary);
  color: #FFFFFF;
  border: 0.33px solid var(--border-primary);
  backdrop-filter: var(--blur-lg);
  -webkit-backdrop-filter: var(--blur-lg);
}

.btn-secondary:hover {
  background: var(--surface-tertiary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
}

/* Success Button - UIKit Green */
.btn-success {
  background: var(--system-green);
  color: #FFFFFF;
  box-shadow: var(--shadow-md);
}

.btn-success:hover {
  background: #248A3D;
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Danger Button - UIKit Red */
.btn-danger {
  background: var(--system-red);
  color: #FFFFFF;
  box-shadow: var(--shadow-md);
}

.btn-danger:hover {
  background: #D70015;
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Warning Button - UIKit Orange */
.btn-warning {
  background: var(--system-orange);
  color: #FFFFFF;
  box-shadow: var(--shadow-md);
}

.btn-warning:hover {
  background: #E6780A;
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Button Sizes - Apple HIG */
.btn-sm {
  padding: 8px 16px;
  font-size: var(--footnote);
  min-height: 36px;
  border-radius: 18px;
}

.btn-lg {
  padding: 12px 24px;
  font-size: var(--body);
  min-height: 50px;
  border-radius: 25px;
}

.btn-xl {
  padding: 16px 32px;
  font-size: var(--headline);
  min-height: 56px;
  border-radius: 28px;
}

/* Premium Typography System */
h1, h2, h3, h4, h5, h6 {
  color: #FFFFFF;
  font-weight: 700;
  letter-spacing: var(--tracking-tight);
  line-height: var(--leading-tight);
  margin: 0;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
}

/* Official Apple Text Styles */

/* Large Title */
h1, .text-large-title {
  font-size: var(--large-title);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.41px;
}

/* Title 1 */
h2, .text-title-1 {
  font-size: var(--title-1);
  font-weight: 700;
  line-height: 1.25;
  letter-spacing: -0.41px;
}

/* Title 2 */
h3, .text-title-2 {
  font-size: var(--title-2);
  font-weight: 600;
  line-height: 1.27;
  letter-spacing: -0.26px;
}

/* Title 3 */
h4, .text-title-3 {
  font-size: var(--title-3);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.45px;
}

/* Headline */
h5, .text-headline {
  font-size: var(--headline);
  font-weight: 600;
  line-height: 1.29;
  letter-spacing: -0.41px;
}

/* Body */
.text-body {
  font-size: var(--body);
  font-weight: 400;
  line-height: 1.29;
  letter-spacing: -0.41px;
}

/* Callout */
.text-callout {
  font-size: var(--callout);
  font-weight: 400;
  line-height: 1.31;
  letter-spacing: -0.32px;
}

/* Subheadline */
.text-subheadline {
  font-size: var(--subheadline);
  font-weight: 400;
  line-height: 1.33;
  letter-spacing: -0.24px;
}

/* Footnote */
.text-footnote {
  font-size: var(--footnote);
  font-weight: 400;
  line-height: 1.38;
  letter-spacing: -0.08px;
}

/* Caption 1 */
.text-caption-1 {
  font-size: var(--caption-1);
  font-weight: 400;
  line-height: 1.33;
  letter-spacing: 0px;
}

/* Caption 2 */
.text-caption-2 {
  font-size: var(--caption-2);
  font-weight: 400;
  line-height: 1.36;
  letter-spacing: 0.06px;
}

/* Semantic Color Classes */
.text-primary {
  color: var(--color-primary);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-danger {
  color: var(--color-danger);
}

.text-info {
  color: var(--color-info);
}

/* Official UIKit System Colors */
.text-blue {
  color: var(--system-blue);
}

.text-green {
  color: var(--system-green);
}

.text-orange {
  color: var(--system-orange);
}

.text-red {
  color: var(--system-red);
}

.text-purple {
  color: var(--system-purple);
}

.text-pink {
  color: var(--system-pink);
}

.text-yellow {
  color: var(--system-yellow);
}

.text-indigo {
  color: var(--system-indigo);
}

.text-teal {
  color: var(--system-teal);
}

.text-brown {
  color: var(--system-brown);
}

.text-cyan {
  color: var(--system-cyan);
}

.text-mint {
  color: var(--system-mint);
}

/* Official UIKit Label Colors */
.text-primary-label {
  color: var(--label);
}

.text-secondary-label {
  color: var(--secondary-label);
}

.text-tertiary-label {
  color: var(--tertiary-label);
}

.text-quaternary-label {
  color: var(--quaternary-label);
}

/* Weight Classes */
.font-thin {
  font-weight: 100;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
}

/* Official UIKit Input System */
.input {
  /* Base Structure */
  background: var(--tertiary-system-background);
  border: 1px solid var(--separator);
  border-radius: 12px;
  padding: 12px 16px;

  /* Typography */
  color: var(--label);
  font-size: var(--body);
  font-weight: 400;
  font-family: inherit;
  line-height: 1.29;
  letter-spacing: -0.41px;

  /* Layout */
  width: 100%;
  min-height: 44px; /* iOS minimum touch target */

  /* Interaction */
  transition: all var(--transition-base) var(--ease-ios);
  backdrop-filter: var(--blur-lg);
  -webkit-backdrop-filter: var(--blur-lg);

  /* Accessibility */
  -webkit-appearance: none;
  appearance: none;
  -webkit-tap-highlight-color: transparent;
}

.input:focus {
  outline: none;
  border-color: var(--system-blue);
  background: var(--secondary-system-background);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.15);
}

.input:hover:not(:focus) {
  border-color: var(--opaque-separator);
  background: var(--secondary-system-background);
}

.input::placeholder {
  color: var(--tertiary-label);
  font-weight: 400;
  opacity: 1;
}

.input:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: var(--surface-primary);
}

/* Input Variants */
.input-success {
  border-color: var(--ios-green);
}

.input-success:focus {
  border-color: var(--ios-green);
  box-shadow:
    var(--shadow-md),
    0 0 0 3px rgba(48, 209, 88, 0.15);
}

.input-error {
  border-color: var(--ios-red);
}

.input-error:focus {
  border-color: var(--ios-red);
  box-shadow:
    var(--shadow-md),
    0 0 0 3px rgba(255, 69, 58, 0.15);
}

/* Input Sizes */
.input-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  min-height: 36px;
  border-radius: var(--radius-md);
}

.input-lg {
  padding: var(--space-4) var(--space-5);
  font-size: var(--text-lg);
  min-height: 52px;
  border-radius: var(--radius-xl);
}

/* Premium Animation System */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(var(--space-3));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(var(--space-8));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(calc(-1 * var(--space-8)));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn var(--transition-slow) var(--ease-ios);
}

.animate-slide-in-up {
  animation: slideInUp var(--transition-slow) var(--ease-ios);
}

.animate-slide-in-down {
  animation: slideInDown var(--transition-slow) var(--ease-ios);
}

.animate-scale-in {
  animation: scaleIn var(--transition-base) var(--ease-ios);
}

.animate-pulse {
  animation: pulse 2s var(--ease-ios) infinite;
}

/* Premium Scrollbar */
::-webkit-scrollbar {
  width: 3px;
  height: 3px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-3);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-2);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: var(--space-1);
}

.gap-2 {
  gap: var(--space-2);
}

.gap-3 {
  gap: var(--space-3);
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.overflow-hidden {
  overflow: hidden;
}

.pointer-events-none {
  pointer-events: none;
}

/* Spacing Utilities */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }

.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }

/* Border Radius Utilities */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
