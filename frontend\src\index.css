:root {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color: #ffffff;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Casino theme colors */
  --primary-gold: #ffd700;
  --primary-green: #00ff88;
  --danger-red: #ff4757;
  --warning-orange: #ffa502;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-glow: 0 8px 32px rgba(0, 255, 136, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Glass morphism effect */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: var(--shadow-glow);
}

/* Button styles */
.btn {
  border-radius: 12px;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-green), #00cc6a);
  color: #000;
  box-shadow: 0 4px 15px rgba(0, 255, 136, 0.4);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 255, 136, 0.6);
}

.btn-secondary {
  background: var(--glass-bg);
  color: #fff;
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-red), #ff3742);
  color: #fff;
  box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 71, 87, 0.6);
}

/* Text styles */
h1, h2, h3, h4, h5, h6 {
  color: #fff;
  font-weight: 700;
}

.text-gold {
  color: var(--primary-gold);
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.text-green {
  color: var(--primary-green);
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

/* Input styles */
.input {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 12px 16px;
  color: #fff;
  font-size: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(0, 255, 136, 0.2);
}

.input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Animations */
@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px var(--primary-green); }
  50% { box-shadow: 0 0 20px var(--primary-green), 0 0 30px var(--primary-green); }
}

.glow-animation {
  animation: glow 2s ease-in-out infinite;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00cc6a;
}
