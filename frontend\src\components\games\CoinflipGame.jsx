import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Coins,
  Plus,
  Play,
  Users,
  Loader2
} from 'lucide-react';
import ApiService from '../../services/ApiService';
import './Games.css';

const CoinflipGame = ({ user, balance, updateBalance }) => {
  const [activeTab, setActiveTab] = useState('rooms');
  const [waitingRooms, setWaitingRooms] = useState([]);
  const [stake, setStake] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadWaitingRooms();
  }, []);

  const loadWaitingRooms = async () => {
    try {
      const rooms = await ApiService.getWaitingRooms();
      setWaitingRooms(rooms);
    } catch (error) {
      console.error('Error loading rooms:', error);
    }
  };

  const createRoom = async () => {
    if (!stake || parseFloat(stake) <= 0) {
      alert('Введите корректную сумму ставки');
      return;
    }

    if (parseFloat(stake) > balance) {
      alert('Недостаточно средств');
      return;
    }

    setLoading(true);
    try {
      const room = await ApiService.createCoinflipRoom(user.telegram_id, parseFloat(stake));
      if (room) {
        updateBalance(balance - parseFloat(stake));
        setStake('');
        loadWaitingRooms();
        alert('Комната создана! Ожидайте соперника.');
      }
    } catch (error) {
      console.error('Error creating room:', error);
      alert('Ошибка при создании комнаты');
    } finally {
      setLoading(false);
    }
  };

  const joinRoom = async (roomId, roomStake) => {
    if (roomStake > balance) {
      alert('Недостаточно средств');
      return;
    }

    setLoading(true);
    try {
      const result = await ApiService.joinCoinflipRoom(roomId, user.telegram_id);
      if (result.success) {
        const newBalance = balance - roomStake + (result.winner === user.telegram_id ? roomStake * 1.8 : 0);
        updateBalance(newBalance);
        loadWaitingRooms();
        
        if (result.winner === user.telegram_id) {
          alert(`Победа! Результат: ${result.result}. Выигрыш: ${(roomStake * 1.8).toFixed(2)} TON`);
        } else {
          alert(`Поражение! Результат: ${result.result}.`);
        }
      }
    } catch (error) {
      console.error('Error joining room:', error);
      alert('Ошибка при присоединении к комнате');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="coinflip-game">
      <motion.div
        className="game-header card-elevated"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-title-1 flex items-center gap-3">
          <Coins size={28} className="text-warning" />
          Coinflip PvP
        </h2>
        <p className="text-callout text-secondary-label">Создайте комнату или присоединитесь к игре</p>
      </motion.div>

      <div className="game-tabs card">
        <button
          className={`tab-btn ${activeTab === 'rooms' ? 'active' : ''}`}
          onClick={() => setActiveTab('rooms')}
        >
          <Users size={20} />
          <span>Комнаты</span>
        </button>
        <button
          className={`tab-btn ${activeTab === 'create' ? 'active' : ''}`}
          onClick={() => setActiveTab('create')}
        >
          <Plus size={20} />
          <span>Создать</span>
        </button>
      </div>

      {activeTab === 'rooms' && (
        <motion.div
          className="rooms-list card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="text-title-2">Открытые комнаты</h3>
          {waitingRooms.length === 0 ? (
            <div className="no-rooms">
              <Coins size={48} className="text-warning" />
              <p className="text-callout">Нет открытых комнат</p>
              <p className="text-footnote text-secondary-label">Создайте свою комнату!</p>
            </div>
          ) : (
            <div className="rooms-grid">
              {waitingRooms.map((room) => (
                <div key={room.id} className="room-card card">
                  <div className="room-stake">
                    <Coins size={20} className="text-success" />
                    <span className="text-headline font-semibold">{parseFloat(room.stake).toFixed(2)} TON</span>
                  </div>
                  <button
                    className="btn btn-primary"
                    onClick={() => joinRoom(room.id, parseFloat(room.stake))}
                    disabled={loading}
                  >
                    <Play size={16} />
                    <span>Играть</span>
                  </button>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      )}

      {activeTab === 'create' && (
        <motion.div
          className="create-room card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="text-title-2">Создать комнату</h3>
          <div className="create-form">
            <label className="text-subheadline font-medium">Сумма ставки (TON)</label>
            <input
              type="number"
              value={stake}
              onChange={(e) => setStake(e.target.value)}
              placeholder="0.00"
              className="input"
              disabled={loading}
              step="0.01"
              min="0.01"
            />
            <button
              className="btn btn-primary btn-lg w-full"
              onClick={createRoom}
              disabled={loading || !stake}
            >
              {loading ? (
                <Loader2 size={20} className="animate-pulse" />
              ) : (
                <Plus size={20} />
              )}
              <span>{loading ? 'Создание...' : 'Создать комнату'}</span>
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CoinflipGame;
