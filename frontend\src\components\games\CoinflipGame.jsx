import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faCoins, 
  faPlus, 
  faPlay,
  faUsers,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import ApiService from '../../services/ApiService';

const CoinflipGame = ({ user, balance, updateBalance }) => {
  const [activeTab, setActiveTab] = useState('rooms');
  const [waitingRooms, setWaitingRooms] = useState([]);
  const [stake, setStake] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadWaitingRooms();
  }, []);

  const loadWaitingRooms = async () => {
    try {
      const rooms = await ApiService.getWaitingRooms();
      setWaitingRooms(rooms);
    } catch (error) {
      console.error('Error loading rooms:', error);
    }
  };

  const createRoom = async () => {
    if (!stake || parseFloat(stake) <= 0) {
      alert('Введите корректную сумму ставки');
      return;
    }

    if (parseFloat(stake) > balance) {
      alert('Недостаточно средств');
      return;
    }

    setLoading(true);
    try {
      const room = await ApiService.createCoinflipRoom(user.telegram_id, parseFloat(stake));
      if (room) {
        updateBalance(balance - parseFloat(stake));
        setStake('');
        loadWaitingRooms();
        alert('Комната создана! Ожидайте соперника.');
      }
    } catch (error) {
      console.error('Error creating room:', error);
      alert('Ошибка при создании комнаты');
    } finally {
      setLoading(false);
    }
  };

  const joinRoom = async (roomId, roomStake) => {
    if (roomStake > balance) {
      alert('Недостаточно средств');
      return;
    }

    setLoading(true);
    try {
      const result = await ApiService.joinCoinflipRoom(roomId, user.telegram_id);
      if (result.success) {
        const newBalance = balance - roomStake + (result.winner === user.telegram_id ? roomStake * 1.8 : 0);
        updateBalance(newBalance);
        loadWaitingRooms();
        
        if (result.winner === user.telegram_id) {
          alert(`Победа! Результат: ${result.result}. Выигрыш: ${(roomStake * 1.8).toFixed(2)} TON`);
        } else {
          alert(`Поражение! Результат: ${result.result}.`);
        }
      }
    } catch (error) {
      console.error('Error joining room:', error);
      alert('Ошибка при присоединении к комнате');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="coinflip-game">
      <motion.div
        className="game-header glass"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h2>
          <FontAwesomeIcon icon={faCoins} className="text-gold" />
          Coinflip PvP
        </h2>
        <p>Создайте комнату или присоединитесь к игре</p>
      </motion.div>

      <div className="game-tabs glass">
        <button
          className={`tab-btn ${activeTab === 'rooms' ? 'active' : ''}`}
          onClick={() => setActiveTab('rooms')}
        >
          <FontAwesomeIcon icon={faUsers} />
          Комнаты
        </button>
        <button
          className={`tab-btn ${activeTab === 'create' ? 'active' : ''}`}
          onClick={() => setActiveTab('create')}
        >
          <FontAwesomeIcon icon={faPlus} />
          Создать
        </button>
      </div>

      {activeTab === 'rooms' && (
        <motion.div
          className="rooms-list glass"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <h3>Открытые комнаты</h3>
          {waitingRooms.length === 0 ? (
            <div className="no-rooms">
              <FontAwesomeIcon icon={faCoins} size="2x" className="text-gold" />
              <p>Нет открытых комнат</p>
              <p>Создайте свою комнату!</p>
            </div>
          ) : (
            <div className="rooms-grid">
              {waitingRooms.map((room) => (
                <div key={room.id} className="room-card">
                  <div className="room-stake">
                    <FontAwesomeIcon icon={faCoins} className="text-green" />
                    <span>{parseFloat(room.stake).toFixed(2)} TON</span>
                  </div>
                  <button
                    className="btn btn-primary"
                    onClick={() => joinRoom(room.id, parseFloat(room.stake))}
                    disabled={loading}
                  >
                    <FontAwesomeIcon icon={faPlay} />
                    Играть
                  </button>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      )}

      {activeTab === 'create' && (
        <motion.div
          className="create-room glass"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <h3>Создать комнату</h3>
          <div className="create-form">
            <label>Сумма ставки (TON)</label>
            <input
              type="number"
              value={stake}
              onChange={(e) => setStake(e.target.value)}
              placeholder="0.00"
              className="input"
              disabled={loading}
              step="0.01"
              min="0.01"
            />
            <button
              className="btn btn-primary"
              onClick={createRoom}
              disabled={loading || !stake}
            >
              {loading ? (
                <FontAwesomeIcon icon={faSpinner} className="fa-spin" />
              ) : (
                <FontAwesomeIcon icon={faPlus} />
              )}
              {loading ? 'Создание...' : 'Создать комнату'}
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CoinflipGame;
