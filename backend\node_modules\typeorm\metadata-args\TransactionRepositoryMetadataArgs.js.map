{"version": 3, "sources": ["../../src/metadata-args/TransactionRepositoryMetadataArgs.ts"], "names": [], "mappings": "", "file": "TransactionRepositoryMetadataArgs.js", "sourcesContent": ["/**\n * Used to inject transaction's repository into the method wrapped with @Transaction decorator.\n */\nexport interface TransactionRepositoryMetadataArgs {\n    /**\n     * Target class on which decorator is used.\n     */\n    readonly target: Function\n\n    /**\n     * Method on which decorator is used.\n     */\n    readonly methodName: string\n\n    /**\n     * Index of the parameter on which decorator is used.\n     */\n    readonly index: number\n\n    /**\n     * Type of the repository class (Repository, TreeRepository or MongoRepository) or custom repository class.\n     */\n    readonly repositoryType: Function\n\n    /**\n     * Argument of generic Repository<T> class if it's not custom repository class.\n     */\n    readonly entityType?: Function\n}\n"], "sourceRoot": ".."}