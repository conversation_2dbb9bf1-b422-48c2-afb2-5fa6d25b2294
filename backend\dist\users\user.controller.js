"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const user_service_1 = require("./user.service");
let UserController = class UserController {
    userService;
    constructor(userService) {
        this.userService = userService;
    }
    async createUser(body) {
        if (!body.telegram_id || !body.username || !body.firstname) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        return this.userService.createUser(body.telegram_id, body.username, body.firstname);
    }
    async updateBalance(body) {
        if (!body.telegram_id || body.amount === undefined) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        const user = await this.userService.updateBalance(body.telegram_id, body.amount);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async deductBalance(body) {
        if (!body.telegram_id || body.amount === undefined) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        const success = await this.userService.deductBalance(body.telegram_id, body.amount);
        if (!success) {
            throw new common_1.BadRequestException('Insufficient balance or user not found');
        }
        return { success: true };
    }
    async getLeaderboard() {
        return this.userService.getLeaderboard();
    }
    async getUser(telegram_id) {
        const user = await this.userService.getUserById(telegram_id);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return {
            telegram_id: user.telegram_id,
            username: user.username,
            firstname: user.firstname,
            balance_ton: user.balance_ton,
            ton_wallet_address: user.ton_wallet_address,
        };
    }
    async banUser(body) {
        if (!body.telegram_id) {
            throw new common_1.BadRequestException('Missing telegram_id');
        }
        const success = await this.userService.banUser(body.telegram_id);
        if (!success) {
            throw new common_1.NotFoundException('User not found');
        }
        return { success: true };
    }
    async unbanUser(body) {
        if (!body.telegram_id) {
            throw new common_1.BadRequestException('Missing telegram_id');
        }
        const success = await this.userService.unbanUser(body.telegram_id);
        if (!success) {
            throw new common_1.NotFoundException('User not found');
        }
        return { success: true };
    }
    async updateWallet(body) {
        if (!body.telegram_id || !body.wallet_address) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        const user = await this.userService.updateWalletAddress(body.telegram_id, body.wallet_address);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "createUser", null);
__decorate([
    (0, common_1.Post)('update-balance'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateBalance", null);
__decorate([
    (0, common_1.Post)('deduct-balance'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "deductBalance", null);
__decorate([
    (0, common_1.Get)('leaderboard'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getLeaderboard", null);
__decorate([
    (0, common_1.Get)(':telegram_id'),
    __param(0, (0, common_1.Param)('telegram_id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUser", null);
__decorate([
    (0, common_1.Post)('ban'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "banUser", null);
__decorate([
    (0, common_1.Post)('unban'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "unbanUser", null);
__decorate([
    (0, common_1.Post)('update-wallet'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateWallet", null);
exports.UserController = UserController = __decorate([
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [user_service_1.UserService])
], UserController);
//# sourceMappingURL=user.controller.js.map