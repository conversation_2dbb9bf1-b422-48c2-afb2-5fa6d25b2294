import { Repository } from 'typeorm';
import { User } from './user.entity';
export declare class UserService {
    private userRepository;
    constructor(userRepository: Repository<User>);
    createUser(telegram_id: string, username: string, firstname: string): Promise<User>;
    getUser(telegram_id: string): Promise<User | null>;
    updateBalance(telegram_id: string, amount: number): Promise<User | null>;
    deductBalance(telegram_id: string, amount: number): Promise<boolean>;
    getLeaderboard(): Promise<User[]>;
    getUserById(telegram_id: string): Promise<User | null>;
    banUser(telegram_id: string): Promise<boolean>;
    unbanUser(telegram_id: string): Promise<boolean>;
    updateWalletAddress(telegram_id: string, wallet_address: string): Promise<User | null>;
}
