{"version": 3, "sources": ["../browser/src/decorator/options/UniqueOptions.ts"], "names": [], "mappings": "", "file": "UniqueOptions.js", "sourcesContent": ["import { DeferrableType } from \"../../metadata/types/DeferrableType\"\n\n/**\n * Describes all unique options.\n */\nexport interface UniqueOptions {\n    /**\n     * Indicate if unique constraints can be deferred.\n     */\n    deferrable?: DeferrableType\n}\n"], "sourceRoot": "../.."}