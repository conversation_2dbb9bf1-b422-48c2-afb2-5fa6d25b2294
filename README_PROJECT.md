# PEPE CAS - Telegram Mini App Casino

Полнофункциональное казино в виде Telegram Mini App с интеграцией TON блокчейна и NFT подарками.

## 🎮 Особенности

- **3 игровых режима**: Crash, Coinflip PvP, Double Roulette
- **TON интеграция**: Пополнение и вывод через TON кошельки
- **NFT подарки**: Пополнение баланса через Telegram Gifts
- **Реальное время**: PvP игры с живыми соперниками
- **Админ панель**: Управление ценами, модификаторами, статистикой
- **Мобильный дизайн**: Адаптивный интерфейс для Telegram

## 🏗️ Архитектура

### Backend (NestJS)
- **База данных**: SQLite с TypeORM
- **API**: RESTful endpoints для всех операций
- **Игровая логика**: Криптографически безопасный рандом
- **Транзакции**: Полная история операций

### Frontend (React + Vite)
- **UI Framework**: React с Framer Motion анимациями
- **Стили**: CSS с glass morphism эффектами
- **Telegram SDK**: Интеграция с Telegram Mini Apps
- **TON Connect**: Подключение TON кошельков

## 🚀 Быстрый запуск

### Предварительные требования
- Node.js 18+
- npm или yarn

### 1. Установка зависимостей

```bash
# Backend
cd backend
npm install

# Frontend
cd ../frontend
npm install
```

### 2. Запуск Backend

```bash
cd backend
npm run build
npm start
```

Backend будет доступен на `http://localhost:3000`

### 3. Запуск Frontend

```bash
cd frontend
npm run dev
```

Frontend будет доступен на `http://localhost:5173`

## 🎯 API Endpoints

### Пользователи
- `POST /users/create` - Создание пользователя
- `GET /users/:telegram_id` - Получение пользователя
- `POST /users/update-balance` - Обновление баланса
- `GET /users/leaderboard` - Таблица лидеров

### Игры
- `POST /games/crash/play` - Игра Crash
- `POST /games/double/play` - Игра Double
- `POST /games/coinflip/create` - Создание Coinflip комнаты
- `POST /games/coinflip/join` - Присоединение к комнате
- `GET /games/coinflip/waiting` - Список ожидающих комнат

### Цены NFT
- `GET /prices` - Все цены
- `POST /prices/update` - Обновление цены
- `POST /prices/import` - Импорт из Excel

### Транзакции
- `GET /transactions/user/:user_id` - История пользователя
- `GET /transactions/stats/:user_id` - Статистика игр

## 🎮 Игровые режимы

### Crash
- Множитель растет от 1.00x
- Игрок выбирает цель и ставку
- Нужно забрать до краха
- Максимальный множитель: 100x

### Coinflip PvP
- Дуэли между игроками
- Ставка 1:1, победитель получает 1.8x (10% комиссия)
- Честный бросок монеты
- Создание и присоединение к комнатам

### Double Roulette
- 4 сектора: 2x (45%), 3x (30%), 5x (20%), 20x (5%)
- Выбор множителя и ставки
- Мгновенный результат

## 💰 Финансовая система

### Пополнение
1. **NFT подарки**: Отправка подарков боту @pepecas_receiver
2. **TON кошелек**: Прямой перевод на персональный адрес

### Вывод
- Только через NFT подарки
- Выбор из доступных моделей
- Отправка через бота с комментарием "PEPE CAS"

## 🔧 Конфигурация

### Переменные окружения Backend
```env
PORT=3000
DATABASE_URL=database.sqlite
```

### Telegram Bot
- Token: `**********************************************`
- Receiver Bot: `@pepecas_receiver`
- API ID: `13434740`
- API Hash: `ddcb8777d24aa5a651a5bab7e17c585d`

## 📊 Админ функции

### Win Modifier
- Глобальный модификатор шансов (-30% до +30%)
- Влияет на все игры
- Настройка через API: `POST /config/win-modifier`

### Управление ценами
- Импорт Excel файлов с ценами NFT
- Колонки: model_key, min_ton, image_url
- Автоматическое обновление

### Мониторинг
- Логи всех транзакций
- Статистика по играм
- Система банов пользователей

## 🧪 Тестирование

### Создание тестового пользователя
```bash
curl -X POST http://localhost:3000/users/create \
  -H "Content-Type: application/json" \
  -d '{"telegram_id":"test_user_123","username":"testuser","firstname":"Test User"}'
```

### Добавление баланса
```bash
curl -X POST http://localhost:3000/users/update-balance \
  -H "Content-Type: application/json" \
  -d '{"telegram_id":"test_user_123","amount":10.5}'
```

### Тест игры Crash
```bash
curl -X POST http://localhost:3000/games/crash/play \
  -H "Content-Type: application/json" \
  -d '{"user_id":"test_user_123","bet_amount":1.0,"target_multiplier":2.5}'
```

## 📱 Telegram Mini App

### Настройка в BotFather
1. Создать бота через @BotFather
2. Настроить Mini App: `/newapp`
3. Указать URL: `https://your-domain.com`
4. Загрузить иконку и описание

### Деплой
- Frontend: Vercel, Netlify, или любой статический хостинг
- Backend: Railway, Heroku, или VPS
- База данных: PostgreSQL для продакшена

## 🔒 Безопасность

- Криптографически стойкий генератор случайных чисел
- Проверка балансов перед каждой операцией
- Защита от дублирования транзакций
- Валидация всех входных данных

## 📈 Масштабирование

- Redis для кеширования активных игр
- WebSocket для реального времени
- Микросервисная архитектура
- Горизонтальное масштабирование

## 🤝 Поддержка

Для вопросов и поддержки:
- Telegram: @pepecas_support
- Email: <EMAIL>

## 📄 Лицензия

MIT License - см. файл LICENSE для деталей.

---

**PEPE CAS** - Будущее азартных игр в Telegram! 🎰🚀
