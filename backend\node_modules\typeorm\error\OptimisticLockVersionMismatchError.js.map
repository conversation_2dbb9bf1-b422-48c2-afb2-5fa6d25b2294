{"version": 3, "sources": ["../../src/error/OptimisticLockVersionMismatchError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,kCAAmC,SAAQ,2BAAY;IAChE,YACI,MAAc,EACd,eAA8B,EAC9B,aAA4B;QAE5B,KAAK,CACD,iCAAiC,MAAM,oBAAoB,eAAe,kCAAkC,aAAa,GAAG,CAC/H,CAAA;IACL,CAAC;CACJ;AAVD,gFAUC", "file": "OptimisticLockVersionMismatchError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when a version check on an object that uses optimistic locking through a version field fails.\n */\nexport class OptimisticLockVersionMismatchError extends TypeORMError {\n    constructor(\n        entity: string,\n        expectedVersion: number | Date,\n        actualVersion: number | Date,\n    ) {\n        super(\n            `The optimistic lock on entity ${entity} failed, version ${expectedVersion} was expected, but is actually ${actualVersion}.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}