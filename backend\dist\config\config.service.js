"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const global_config_entity_1 = require("./global-config.entity");
let ConfigService = class ConfigService {
    configRepository;
    constructor(configRepository) {
        this.configRepository = configRepository;
    }
    async getConfig(key) {
        const config = await this.configRepository.findOne({ where: { key } });
        return config ? config.value : null;
    }
    async setConfig(key, value) {
        let config = await this.configRepository.findOne({ where: { key } });
        if (config) {
            config.value = value;
        }
        else {
            config = this.configRepository.create({ key, value });
        }
        return this.configRepository.save(config);
    }
    async getWinModifier() {
        const modifier = await this.getConfig('win_modifier');
        return modifier ? parseFloat(modifier) : 0;
    }
    async setWinModifier(modifier) {
        const clampedModifier = Math.max(-0.3, Math.min(0.3, modifier));
        await this.setConfig('win_modifier', clampedModifier.toString());
    }
    async getAllConfigs() {
        return this.configRepository.find();
    }
    async initializeDefaults() {
        const defaults = [
            { key: 'win_modifier', value: '0' },
            { key: 'last_price_import', value: new Date().toISOString() },
        ];
        for (const defaultConfig of defaults) {
            const existing = await this.getConfig(defaultConfig.key);
            if (!existing) {
                await this.setConfig(defaultConfig.key, defaultConfig.value);
            }
        }
    }
};
exports.ConfigService = ConfigService;
exports.ConfigService = ConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(global_config_entity_1.GlobalConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ConfigService);
//# sourceMappingURL=config.service.js.map