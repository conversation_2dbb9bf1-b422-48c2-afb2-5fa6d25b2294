import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHome,
  faGamepad,
  faWallet,
  faHistory,
  faUser,
  faCoins,
  faTrophy,
  faGift
} from '@fortawesome/free-solid-svg-icons';
import './App.css';

// Import components
import Home from './components/Home';
import Deposit from './components/Deposit';
import Games from './components/Games';
import Withdraw from './components/Withdraw';
import History from './components/History';
import Profile from './components/Profile';
import ApiService from './services/ApiService';

function App() {
  const [currentPage, setCurrentPage] = useState('home');
  const [user, setUser] = useState(null);
  const [balance, setBalance] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeUser();
  }, []);

  const initializeUser = async () => {
    try {
      if (window.userId && window.firstName) {
        // Try to get existing user
        let userData = await ApiService.getUser(window.userId);

        if (!userData) {
          // Create new user if doesn't exist
          userData = await ApiService.createUser({
            telegram_id: window.userId,
            username: window.username || 'user',
            firstname: window.firstName
          });
        }

        setUser(userData);
        setBalance(parseFloat(userData.balance_ton || 0));
      }
    } catch (error) {
      console.error('Error initializing user:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateBalance = (newBalance) => {
    setBalance(newBalance);
  };

  const navigation = [
    { id: 'home', icon: faHome, label: 'Главная' },
    { id: 'deposit', icon: faWallet, label: 'Пополнить' },
    { id: 'games', icon: faGamepad, label: 'Играть' },
    { id: 'withdraw', icon: faGift, label: 'Вывести' },
    { id: 'history', icon: faHistory, label: 'История' },
    { id: 'profile', icon: faUser, label: 'Профиль' },
  ];

  const renderPage = () => {
    const pageProps = { user, balance, updateBalance };

    switch (currentPage) {
      case 'home':
        return <Home {...pageProps} />;
      case 'deposit':
        return <Deposit {...pageProps} />;
      case 'games':
        return <Games {...pageProps} />;
      case 'withdraw':
        return <Withdraw {...pageProps} />;
      case 'history':
        return <History {...pageProps} />;
      case 'profile':
        return <Profile {...pageProps} />;
      default:
        return <Home {...pageProps} />;
    }
  };

  if (loading) {
    return (
      <div className="loading-screen">
        <motion.div
          className="loading-spinner"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <FontAwesomeIcon icon={faCoins} size="3x" className="text-gold" />
        </motion.div>
        <h2 className="text-gold">PEPE CAS</h2>
        <p>Загрузка...</p>
      </div>
    );
  }

  return (
    <div className="app">
      {/* Header */}
      <header className="app-header glass">
        <div className="header-content">
          <div className="logo">
            <FontAwesomeIcon icon={faTrophy} className="text-gold" />
            <span className="text-gold">PEPE CAS</span>
          </div>
          <div className="balance">
            <FontAwesomeIcon icon={faCoins} className="text-green" />
            <span className="text-green">{balance.toFixed(2)} TON</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="app-main">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentPage}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="page-content"
          >
            {renderPage()}
          </motion.div>
        </AnimatePresence>
      </main>

      {/* Bottom Navigation */}
      <nav className="bottom-nav glass">
        {navigation.map((item) => (
          <motion.button
            key={item.id}
            className={`nav-item ${currentPage === item.id ? 'active' : ''}`}
            onClick={() => setCurrentPage(item.id)}
            whileTap={{ scale: 0.95 }}
          >
            <FontAwesomeIcon icon={item.icon} />
            <span>{item.label}</span>
          </motion.button>
        ))}
      </nav>
    </div>
  );
}

export default App;
