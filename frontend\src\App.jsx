import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home as HomeIcon,
  Gamepad2,
  Wallet,
  History as HistoryIcon,
  User,
  Coins,
  Trophy,
  Gift,
  Loader2
} from 'lucide-react';
import './App.css';

// Import components
import Home from './components/Home';
import Deposit from './components/Deposit';
import Games from './components/Games';
import Withdraw from './components/Withdraw';
import History from './components/History';
import Profile from './components/Profile';
import ApiService from './services/ApiService';

function App() {
  const [currentPage, setCurrentPage] = useState('home');
  const [user, setUser] = useState(null);
  const [balance, setBalance] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    initializeUser();
  }, []);

  const initializeUser = async () => {
    try {
      if (window.userId && window.firstName) {
        // Try to get existing user
        let userData = await ApiService.getUser(window.userId);

        if (!userData) {
          // Create new user if doesn't exist
          userData = await ApiService.createUser({
            telegram_id: window.userId,
            username: window.username || 'user',
            firstname: window.firstName
          });
        }

        setUser(userData);
        setBalance(parseFloat(userData.balance_ton || 0));
      }
    } catch (error) {
      console.error('Error initializing user:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateBalance = (newBalance) => {
    setBalance(newBalance);
  };

  const navigation = [
    { id: 'home', icon: HomeIcon, label: 'Главная' },
    { id: 'deposit', icon: Wallet, label: 'Пополнить' },
    { id: 'games', icon: Gamepad2, label: 'Играть' },
    { id: 'withdraw', icon: Gift, label: 'Вывести' },
    { id: 'history', icon: HistoryIcon, label: 'История' },
    { id: 'profile', icon: User, label: 'Профиль' },
  ];

  const renderPage = () => {
    const pageProps = { user, balance, updateBalance };

    switch (currentPage) {
      case 'home':
        return <Home {...pageProps} />;
      case 'deposit':
        return <Deposit {...pageProps} />;
      case 'games':
        return <Games {...pageProps} />;
      case 'withdraw':
        return <Withdraw {...pageProps} />;
      case 'history':
        return <History {...pageProps} />;
      case 'profile':
        return <Profile {...pageProps} />;
      default:
        return <Home {...pageProps} />;
    }
  };

  if (loading) {
    return (
      <div className="loading-screen">
        <motion.div
          className="loading-spinner"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <Loader2 size={48} className="text-primary" />
        </motion.div>
        <h2 className="text-large-title text-primary">PEPE CAS</h2>
        <p className="text-callout text-secondary-label">Загрузка казино...</p>
      </div>
    );
  }

  return (
    <div className="app">
      {/* Header */}
      <header className="app-header">
        <div className="header-content">
          <div className="logo">
            <Trophy size={20} className="text-orange" />
            <span className="text-orange">PEPE CAS</span>
          </div>
          <div className="balance">
            <Coins size={16} className="text-success" />
            <span className="text-success font-semibold">{balance.toFixed(2)} TON</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="app-main">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentPage}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="page-content"
          >
            {renderPage()}
          </motion.div>
        </AnimatePresence>
      </main>

      {/* Bottom Navigation */}
      <nav className="bottom-nav">
        {navigation.map((item) => {
          const IconComponent = item.icon;
          return (
            <motion.button
              key={item.id}
              className={`nav-item ${currentPage === item.id ? 'active' : ''}`}
              onClick={() => setCurrentPage(item.id)}
              whileTap={{ scale: 0.95 }}
            >
              <IconComponent size={22} />
              <span>{item.label}</span>
            </motion.button>
          );
        })}
      </nav>
    </div>
  );
}

export default App;
