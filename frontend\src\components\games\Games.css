/* Premium Games Styles */

/* Common Game Layout */
.coinflip-game,
.double-game,
.crash-game {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  padding-bottom: var(--space-5);
  min-height: 100%;
}

/* Premium Game Header */
.game-header {
  padding: var(--space-6);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.game-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
}

.game-header h2 {
  margin-bottom: var(--space-2);
}

.game-header p {
  margin: 0;
}

/* Premium Game Tabs */
.game-tabs {
  display: flex;
  padding: var(--space-2);
  gap: var(--space-2);
  background: var(--surface-primary);
  border-radius: var(--radius-2xl);
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: transparent;
  border: none;
  border-radius: var(--radius-xl);
  color: var(--gray-1);
  font-size: var(--text-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-base) var(--ease-ios);
  min-height: 44px;
}

.tab-btn:hover {
  background: var(--surface-secondary);
  color: #FFFFFF;
}

.tab-btn.active {
  background: var(--ios-blue);
  color: #FFFFFF;
  box-shadow: var(--shadow-md);
}

.tab-btn.active:hover {
  background: var(--ios-blue-dark);
}

/* Premium Rooms List */
.rooms-list {
  padding: var(--space-6);
}

.rooms-list h3 {
  margin-bottom: var(--space-4);
  text-align: center;
}

.no-rooms {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  padding: var(--space-8) var(--space-4);
  text-align: center;
}

.rooms-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-3);
}

@media (min-width: 768px) {
  .rooms-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Premium Room Card */
.room-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  position: relative;
  overflow: hidden;
}

.room-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  pointer-events: none;
}

.room-stake {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* Premium Create Room */
.create-room {
  padding: var(--space-6);
}

.create-room h3 {
  margin-bottom: var(--space-4);
  text-align: center;
}

.create-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  max-width: 300px;
  margin: 0 auto;
}

.create-form label {
  color: #FFFFFF;
  margin-bottom: var(--space-1);
}

/* Premium Double Game Styles */
.multipliers-grid {
  padding: var(--space-6);
}

.multipliers {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-3);
  margin-top: var(--space-4);
}

@media (min-width: 768px) {
  .multipliers {
    grid-template-columns: repeat(4, 1fr);
  }
}

.multiplier-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: var(--surface-secondary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-base) var(--ease-ios);
  position: relative;
  overflow: hidden;
}

.multiplier-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity var(--transition-fast) var(--ease-ios);
}

.multiplier-btn:hover::before {
  opacity: 1;
}

.multiplier-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.multiplier-btn.selected {
  background: var(--surface-elevated);
  border-color: var(--ios-blue);
  box-shadow: var(--shadow-md);
}

.multiplier-value {
  font-size: var(--text-xl);
  font-weight: 800;
}

.multiplier-chance {
  font-size: var(--text-sm);
  color: var(--gray-1);
  font-weight: 500;
}

.bet-controls {
  padding: var(--space-6);
}

.bet-input {
  margin: var(--space-4) 0;
}

.quick-bets {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-2);
  margin: var(--space-4) 0;
}

.bet-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.bet-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-3);
}

.bet-button {
  padding: var(--space-4);
  border-radius: var(--radius-xl);
  border: 2px solid;
  background: var(--surface-secondary);
  color: #FFFFFF;
  font-weight: 700;
  font-size: var(--text-lg);
  cursor: pointer;
  transition: all var(--transition-base) var(--ease-ios);
  position: relative;
  overflow: hidden;
}

.bet-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity var(--transition-fast) var(--ease-ios);
}

.bet-button:hover::before {
  opacity: 1;
}

.bet-button.red {
  border-color: var(--ios-red);
  background: linear-gradient(135deg, var(--ios-red), var(--ios-red-dark));
}

.bet-button.red:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.bet-button.black {
  border-color: var(--gray-3);
  background: linear-gradient(135deg, var(--gray-3), var(--gray-4));
}

.bet-button.black:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.bet-button.green {
  border-color: var(--ios-green);
  background: linear-gradient(135deg, var(--ios-green), var(--ios-green-dark));
}

.bet-button.green:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Premium Game Results */
.game-result {
  padding: var(--space-6);
  text-align: center;
  border-radius: var(--radius-2xl);
  margin: var(--space-4) 0;
}

.game-result.win {
  background: linear-gradient(135deg, rgba(48, 209, 88, 0.2), rgba(48, 209, 88, 0.1));
  border: 1px solid rgba(48, 209, 88, 0.3);
}

.game-result.lose {
  background: linear-gradient(135deg, rgba(255, 69, 58, 0.2), rgba(255, 69, 58, 0.1));
  border: 1px solid rgba(255, 69, 58, 0.3);
}

.result-text {
  font-size: var(--text-xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.result-amount {
  font-size: var(--text-lg);
  font-weight: 600;
}

/* Premium Responsive */
@media (max-width: 480px) {
  .game-header {
    padding: var(--space-4);
  }
  
  .rooms-list,
  .create-room,
  .double-controls {
    padding: var(--space-4);
  }
  
  .bet-buttons {
    grid-template-columns: 1fr;
  }
  
  .room-card {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }
}
