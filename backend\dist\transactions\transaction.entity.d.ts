import { User } from '../users/user.entity';
export declare enum TransactionKind {
    DEPOSIT_GIFT = "DEPOSIT_GIFT",
    DEPOSIT_TON = "DEPOSIT_TON",
    WITHDRAW_GIFT = "WITHDRAW_GIFT",
    GAME_BET = "GAME_BET",
    GAME_WIN = "GAME_WIN"
}
export declare class Transaction {
    id: number;
    user: User;
    user_id: string;
    kind: TransactionKind;
    amount_ton: number;
    ref: string;
    game_type: string;
    game_data: string;
    created_at: Date;
}
