import { UserService } from './user.service';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    createUser(body: {
        telegram_id: string;
        username: string;
        firstname: string;
    }): Promise<import("./user.entity").User>;
    updateBalance(body: {
        telegram_id: string;
        amount: number;
    }): Promise<import("./user.entity").User>;
    deductBalance(body: {
        telegram_id: string;
        amount: number;
    }): Promise<{
        success: boolean;
    }>;
    getLeaderboard(): Promise<import("./user.entity").User[]>;
    getUser(telegram_id: string): Promise<{
        telegram_id: string;
        username: string;
        firstname: string;
        balance_ton: number;
        ton_wallet_address: string;
    }>;
    banUser(body: {
        telegram_id: string;
    }): Promise<{
        success: boolean;
    }>;
    unbanUser(body: {
        telegram_id: string;
    }): Promise<{
        success: boolean;
    }>;
    updateWallet(body: {
        telegram_id: string;
        wallet_address: string;
    }): Promise<import("./user.entity").User>;
}
