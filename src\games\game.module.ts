import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CoinflipRoom } from './coinflip-room.entity';
import { GameService } from './game.service';
import { CoinflipService } from './coinflip.service';
import { GameController } from './game.controller';
import { UserModule } from '../users/user.module';
import { TransactionModule } from '../transactions/transaction.module';
import { ConfigModule } from '../config/config.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CoinflipRoom]),
    UserModule,
    TransactionModule,
    ConfigModule,
  ],
  controllers: [GameController],
  providers: [GameService, CoinflipService],
  exports: [GameService, CoinflipService],
})
export class GameModule {}
