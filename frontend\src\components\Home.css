/* Premium Home Component */
.home {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  padding: var(--space-4);
  max-width: 1200px;
  margin: 0 auto;
}

.home-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: var(--space-4);
}

/* Premium Welcome Section */
.welcome-section {
  padding: var(--space-6);
  text-align: center;
  position: relative;
  overflow: hidden;
  border-radius: 24px !important;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
}

.welcome-content h1 {
  margin-bottom: var(--space-2);
  background: linear-gradient(135deg, #FFFFFF, var(--gray-1));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  margin-bottom: var(--space-5);
}

.welcome-stats {
  display: flex;
  justify-content: center;
  gap: var(--space-6);
  margin-top: var(--space-5);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 0.33px solid var(--border-tertiary);
  transition: all var(--transition-base) var(--ease-ios);
}

.stat-item:hover {
  background: var(--surface-tertiary);
  transform: translateY(-1px);
}

/* Premium Quick Actions */
.quick-actions h2 {
  margin-bottom: var(--space-4);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-3);
}

.action-card {
  padding: var(--space-5) var(--space-4);
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  pointer-events: none;
}

.action-icon {
  margin-bottom: var(--space-3);
  transition: all var(--transition-base) var(--ease-ios);
}

.action-card:hover .action-icon {
  transform: scale(1.1);
}

.action-card h3 {
  margin-bottom: var(--space-1);
}

.action-card p {
  margin: 0;
}

/* Premium Leaderboard */
.leaderboard-preview {
  padding: var(--space-6);
}

.leaderboard-preview h2 {
  margin-bottom: var(--space-4);
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.player-rank {
  min-width: 32px;
}

.rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  font-weight: 700;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.rank.top-three {
  background: linear-gradient(135deg, var(--primary-gold), #ffed4e);
  color: #000;
}

.player-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.player-name {
  font-weight: 600;
  color: #fff;
}

.player-balance {
  font-weight: 700;
  font-size: 14px;
}

/* Recent Games */
.recent-games {
  padding: 20px;
}

.recent-games h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  margin-bottom: 16px;
  color: #fff;
}

.games-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.game-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.game-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.game-type {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
}

.game-stake {
  font-size: 12px;
  font-weight: 700;
}

.game-result {
  font-size: 20px;
}

/* Responsive */
@media (max-width: 480px) {
  .welcome-content h1 {
    font-size: 20px;
  }
  
  .welcome-subtitle {
    font-size: 14px;
  }
  
  .welcome-stats {
    gap: 16px;
  }
  
  .stat-item {
    font-size: 12px;
  }
  
  .actions-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }
  
  .action-card {
    padding: 16px 12px;
  }
  
  .action-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  .action-card h3 {
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .action-card p {
    font-size: 11px;
  }
}
