/* Home Component Styles */
.home {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding-bottom: 20px;
}

.home-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 16px;
  color: rgba(255, 255, 255, 0.7);
}

/* Welcome Section */
.welcome-section {
  padding: 24px;
  text-align: center;
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(255, 215, 0, 0.1));
}

.welcome-content h1 {
  font-size: 24px;
  margin-bottom: 8px;
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16px;
}

.welcome-stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

/* Quick Actions */
.quick-actions h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  margin-bottom: 16px;
  color: #fff;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.action-card {
  padding: 20px 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.action-card:hover {
  border-color: var(--primary-green);
  box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.action-card h3 {
  font-size: 16px;
  margin-bottom: 6px;
  color: #fff;
}

.action-card p {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* Leaderboard Preview */
.leaderboard-preview h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  margin-bottom: 16px;
  color: #fff;
}

.leaderboard-preview {
  padding: 20px;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.player-rank {
  min-width: 32px;
}

.rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  font-weight: 700;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.rank.top-three {
  background: linear-gradient(135deg, var(--primary-gold), #ffed4e);
  color: #000;
}

.player-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.player-name {
  font-weight: 600;
  color: #fff;
}

.player-balance {
  font-weight: 700;
  font-size: 14px;
}

/* Recent Games */
.recent-games {
  padding: 20px;
}

.recent-games h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  margin-bottom: 16px;
  color: #fff;
}

.games-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.game-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.game-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.game-type {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
}

.game-stake {
  font-size: 12px;
  font-weight: 700;
}

.game-result {
  font-size: 20px;
}

/* Responsive */
@media (max-width: 480px) {
  .welcome-content h1 {
    font-size: 20px;
  }
  
  .welcome-subtitle {
    font-size: 14px;
  }
  
  .welcome-stats {
    gap: 16px;
  }
  
  .stat-item {
    font-size: 12px;
  }
  
  .actions-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }
  
  .action-card {
    padding: 16px 12px;
  }
  
  .action-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  .action-card h3 {
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .action-card p {
    font-size: 11px;
  }
}
