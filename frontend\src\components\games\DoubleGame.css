/* Double Game Styles */
.double-game {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-4);
}

/* Game Header */
.game-header {
  text-align: center;
  padding: var(--space-6);
  position: relative;
  overflow: hidden;
}

.header-icon {
  margin-bottom: var(--space-4);
  display: flex;
  justify-content: center;
}

.stats-row {
  display: flex;
  justify-content: center;
  gap: var(--space-6);
  margin-top: var(--space-4);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--tertiary-system-background);
  border-radius: 12px;
  border: 1px solid var(--separator);
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--separator);
}

.section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--tertiary-system-background);
  border-radius: 12px;
  border: 1px solid var(--separator);
}

/* Multipliers Grid */
.multipliers-grid {
  padding: var(--space-4);
}

.multipliers {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.multiplier-btn {
  background: var(--tertiary-system-background);
  border: 2px solid var(--separator);
  border-radius: 16px;
  padding: var(--space-4);
  cursor: pointer;
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  min-height: 100px;
  position: relative;
  overflow: hidden;
}

.multiplier-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.multiplier-btn.selected {
  border-width: 3px;
  box-shadow: var(--shadow-md);
}

.multiplier-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.multiplier-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.multiplier-value {
  font-size: var(--title-2);
  font-weight: 700;
  line-height: 1;
}

.multiplier-badge {
  color: #FFFFFF;
  font-size: var(--footnote);
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 8px;
  min-width: 40px;
  text-align: center;
}

.multiplier-description {
  text-align: center;
  margin-top: var(--space-1);
}

/* Bet Controls */
.bet-controls {
  padding: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  z-index: 2;
  pointer-events: none;
}

.input-with-icon {
  padding-left: 48px;
}

.form-hint {
  margin-top: var(--space-1);
}

/* Quick Bets */
.quick-bets {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.quick-bets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--space-2);
}

.quick-bet-btn {
  background: var(--tertiary-system-background);
  border: 1px solid var(--separator);
  border-radius: 12px;
  padding: var(--space-2) var(--space-3);
  color: var(--label);
  font-size: var(--footnote);
  font-weight: 600;
  cursor: pointer;
  transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-height: 36px;
}

.quick-bet-btn:hover {
  background: var(--system-blue);
  color: #FFFFFF;
  border-color: var(--system-blue);
}

.quick-bet-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Play Button */
.play-btn {
  margin-top: var(--space-4);
}

/* Game Result */
.game-result {
  padding: var(--space-6);
  text-align: center;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
}

.game-result.won {
  background: linear-gradient(135deg, rgba(48, 209, 88, 0.1), rgba(48, 209, 88, 0.05));
  border: 2px solid var(--system-green);
}

.game-result.lost {
  background: linear-gradient(135deg, rgba(255, 69, 58, 0.1), rgba(255, 69, 58, 0.05));
  border: 2px solid var(--system-red);
}

.result-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.result-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 20px;
  background: var(--tertiary-system-background);
  border: 1px solid var(--separator);
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.result-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
}

.result-row.payout {
  border-top: 1px solid var(--separator);
  padding-top: var(--space-3);
  margin-top: var(--space-2);
}

/* Animations */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .double-game {
    padding: var(--space-3);
  }
  
  .stats-row {
    gap: var(--space-3);
  }
  
  .multipliers {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-bets-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .game-header {
    padding: var(--space-4);
  }
  
  .header-icon {
    margin-bottom: var(--space-3);
  }
  
  .multipliers {
    grid-template-columns: 1fr;
  }
  
  .multiplier-btn {
    min-height: 80px;
    padding: var(--space-3);
  }
  
  .result-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
}
