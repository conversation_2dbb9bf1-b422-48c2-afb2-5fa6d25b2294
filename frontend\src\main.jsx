import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.jsx';

import { init, initData, miniApp } from '@telegram-apps/sdk';
import { TonConnectUIProvider } from '@tonconnect/ui-react';

const initializeTelegramSDK = async () => {
  try {
    await init();

    if (miniApp.ready.isAvailable()) {
      await miniApp.ready();
      window.dispatchEvent(new Event('miniAppReady'));
    }

    initData.restore();
    initData.state();

    const user = initData.user();
    console.log('Данные пользователя:', user);

    if (user) {
      window.userId = user.id;
      window.firstName = user.first_name;
      window.username = user.username;

      console.log('ID пользователя:', window.userId);
      console.log('Имя пользователя:', window.firstName);

      // Ожидаем полной готовности всех данных
      window.dispatchEvent(new Event('userIdReady'));
    } else {
      console.error('Ошибка: Пользовательские данные не загружены!');
      // Для тестирования в браузере
      window.userId = 'test_user_123';
      window.firstName = 'Test User';
      window.username = 'testuser';
      window.dispatchEvent(new Event('userIdReady'));
    }
  } catch (error) {
    console.error('Ошибка инициализации Telegram Mini App:', error);
    // Для тестирования в браузере
    window.userId = 'test_user_123';
    window.firstName = 'Test User';
    window.username = 'testuser';
    window.dispatchEvent(new Event('userIdReady'));
  }
};

// Дожидаемся обоих событий, затем рендерим приложение
const waitForUserData = async () => {
  await new Promise((resolve) => {
    const checkReady = () => {
      if (window.userId && window.firstName) {
        resolve();
      }
    };

    window.addEventListener('userIdReady', checkReady);
    window.addEventListener('miniAppReady', checkReady);

    checkReady();
  });

  createRoot(document.getElementById('root')).render(
    <StrictMode>
      <TonConnectUIProvider manifestUrl="https://ton-connect.github.io/demo-dapp-with-react-ui/tonconnect-manifest.json">
        <App />
      </TonConnectUIProvider>
    </StrictMode>,
  );
};

initializeTelegramSDK().then(waitForUserData);
