{"version": 3, "sources": ["../../src/find-options/OrderByCondition.ts"], "names": [], "mappings": "", "file": "OrderByCondition.js", "sourcesContent": ["/**\n * Special object that defines order condition for ORDER BY in sql.\n *\n * Example:\n * {\n *  \"name\": \"ASC\",\n *  \"id\": \"DESC\"\n * }\n *\n * @deprecated\n */\nexport type OrderByCondition = {\n    [columnName: string]:\n        | (\"ASC\" | \"DESC\")\n        | {\n              order: \"ASC\" | \"DESC\"\n              nulls?: \"NULLS FIRST\" | \"NULLS LAST\"\n          }\n}\n"], "sourceRoot": ".."}