{"version": 3, "sources": ["../browser/src/driver/oracle/OracleConnectionOptions.ts"], "names": [], "mappings": "", "file": "OracleConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\nimport { OracleConnectionCredentialsOptions } from \"./OracleConnectionCredentialsOptions\"\n\nexport interface OracleThickModeOptions {\n    binaryDir?: string\n    configDir?: string\n    driverName?: string\n    errorUrl?: string\n    libDir?: string\n}\n\n/**\n * Oracle-specific connection options.\n */\nexport interface OracleConnectionOptions\n    extends BaseDataSourceOptions,\n        OracleConnectionCredentialsOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"oracle\"\n\n    /**\n     * Schema name. By default is \"public\".\n     */\n    readonly schema?: string\n\n    /**\n     * The driver object\n     * This defaults to require(\"oracledb\")\n     */\n    readonly driver?: any\n\n    /**\n     * Utilize the thick driver. Starting from oracledb version 6, it's necessary to set this to true when opting for the thick client usage.\n     * Alternatively, an 'OracleThickModeOptions' object can be configured, which is used for the thick mode configuration by passing it to the 'node-oracledb' driver.\n     * For additional information, refer to the details provided in the following link:\n     * (https://node-oracledb.readthedocs.io/en/latest/api_manual/oracledb.html#oracledb.initOracleClient)\n     */\n    readonly thickMode?: boolean | OracleThickModeOptions\n\n    /**\n     * A boolean determining whether to pass time values in UTC or local time. (default: false).\n     */\n    readonly useUTC?: boolean\n\n    /**\n     * Replication setup.\n     */\n    readonly replication?: {\n        /**\n         * Master server used by orm to perform writes.\n         */\n        readonly master: OracleConnectionCredentialsOptions\n\n        /**\n         * List of read-from servers (slaves).\n         */\n        readonly slaves: OracleConnectionCredentialsOptions[]\n    }\n}\n"], "sourceRoot": "../.."}