"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionController = void 0;
const common_1 = require("@nestjs/common");
const transaction_service_1 = require("./transaction.service");
const transaction_entity_1 = require("./transaction.entity");
let TransactionController = class TransactionController {
    transactionService;
    constructor(transactionService) {
        this.transactionService = transactionService;
    }
    async createTransaction(body) {
        if (!body.user_id || !body.kind || body.amount_ton === undefined) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        return this.transactionService.createTransaction(body.user_id, body.kind, body.amount_ton, body.ref, body.game_type, body.game_data);
    }
    async getUserTransactions(user_id, limit) {
        const limitNum = limit ? parseInt(limit) : 50;
        return this.transactionService.getUserTransactions(user_id, limitNum);
    }
    async getAllTransactions(limit) {
        const limitNum = limit ? parseInt(limit) : 100;
        return this.transactionService.getAllTransactions(limitNum);
    }
    async getTransactionsByKind(kind, limit) {
        const limitNum = limit ? parseInt(limit) : 50;
        return this.transactionService.getTransactionsByKind(kind, limitNum);
    }
    async getTransactionsByGame(game_type, limit) {
        const limitNum = limit ? parseInt(limit) : 50;
        return this.transactionService.getTransactionsByGame(game_type, limitNum);
    }
    async getUserGameStats(user_id) {
        return this.transactionService.getUserGameStats(user_id);
    }
};
exports.TransactionController = TransactionController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "createTransaction", null);
__decorate([
    (0, common_1.Get)('user/:user_id'),
    __param(0, (0, common_1.Param)('user_id')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getUserTransactions", null);
__decorate([
    (0, common_1.Get)('all'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getAllTransactions", null);
__decorate([
    (0, common_1.Get)('kind/:kind'),
    __param(0, (0, common_1.Param)('kind')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getTransactionsByKind", null);
__decorate([
    (0, common_1.Get)('game/:game_type'),
    __param(0, (0, common_1.Param)('game_type')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getTransactionsByGame", null);
__decorate([
    (0, common_1.Get)('stats/:user_id'),
    __param(0, (0, common_1.Param)('user_id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getUserGameStats", null);
exports.TransactionController = TransactionController = __decorate([
    (0, common_1.Controller)('transactions'),
    __metadata("design:paramtypes", [transaction_service_1.TransactionService])
], TransactionController);
//# sourceMappingURL=transaction.controller.js.map