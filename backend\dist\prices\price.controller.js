"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PriceController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const price_service_1 = require("./price.service");
let PriceController = class PriceController {
    priceService;
    constructor(priceService) {
        this.priceService = priceService;
    }
    async getAllPrices() {
        return this.priceService.getAllPrices();
    }
    async getPrice(model_key) {
        const price = await this.priceService.getPriceByModelKey(model_key);
        if (!price) {
            throw new common_1.NotFoundException('Price not found');
        }
        return price;
    }
    async updatePrice(body) {
        if (!body.model_key || body.min_ton === undefined || !body.image_url) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        return this.priceService.updatePrice(body.model_key, body.min_ton, body.image_url);
    }
    async importPrices(file) {
        if (!file) {
            throw new common_1.BadRequestException('No file uploaded');
        }
        if (!file.originalname.match(/\.(xlsx|xls)$/)) {
            throw new common_1.BadRequestException('Only Excel files are allowed');
        }
        const result = await this.priceService.importFromExcel(file.buffer);
        return result;
    }
    async clearAllPrices() {
        await this.priceService.clearAllPrices();
        return { success: true };
    }
};
exports.PriceController = PriceController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PriceController.prototype, "getAllPrices", null);
__decorate([
    (0, common_1.Get)(':model_key'),
    __param(0, (0, common_1.Param)('model_key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PriceController.prototype, "getPrice", null);
__decorate([
    (0, common_1.Post)('update'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PriceController.prototype, "updatePrice", null);
__decorate([
    (0, common_1.Post)('import'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PriceController.prototype, "importPrices", null);
__decorate([
    (0, common_1.Post)('clear'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PriceController.prototype, "clearAllPrices", null);
exports.PriceController = PriceController = __decorate([
    (0, common_1.Controller)('prices'),
    __metadata("design:paramtypes", [price_service_1.PriceService])
], PriceController);
//# sourceMappingURL=price.controller.js.map