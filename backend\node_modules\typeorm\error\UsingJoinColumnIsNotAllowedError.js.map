{"version": 3, "sources": ["../../src/error/UsingJoinColumnIsNotAllowedError.ts"], "names": [], "mappings": ";;;AAEA,iDAA6C;AAE7C,MAAa,gCAAiC,SAAQ,2BAAY;IAC9D,YAAY,cAA8B,EAAE,QAA0B;QAClE,KAAK,CACD,uBAAuB,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,aAAa;YAC5E,sEAAsE,CAC7E,CAAA;IACL,CAAC;CACJ;AAPD,4EAOC", "file": "UsingJoinColumnIsNotAllowedError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class UsingJoinColumnIsNotAllowedError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata, relation: RelationMetadata) {\n        super(\n            `Using JoinColumn on ${entityMetadata.name}#${relation.propertyName} is wrong. ` +\n                `You can use JoinColumn only on one-to-one and many-to-one relations.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}