import { Repository } from 'typeorm';
import { Price } from './price.entity';
export declare class PriceService {
    private priceRepository;
    constructor(priceRepository: Repository<Price>);
    getAllPrices(): Promise<Price[]>;
    getPriceByModelKey(model_key: string): Promise<Price | null>;
    updatePrice(model_key: string, min_ton: number, image_url: string): Promise<Price>;
    importFromExcel(buffer: Buffer): Promise<{
        success: boolean;
        imported: number;
        errors: string[];
    }>;
    deletePrice(model_key: string): Promise<boolean>;
    clearAllPrices(): Promise<void>;
}
