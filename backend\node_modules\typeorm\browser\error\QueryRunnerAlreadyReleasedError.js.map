{"version": 3, "sources": ["../browser/src/error/QueryRunnerAlreadyReleasedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,+BAAgC,SAAQ,YAAY;IAC7D;QACI,KAAK,CAAC,4DAA4D,CAAC,CAAA;IACvE,CAAC;CACJ", "file": "QueryRunnerAlreadyReleasedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\nexport class QueryRunnerAlreadyReleasedError extends TypeORMError {\n    constructor() {\n        super(`Query runner already released. Cannot run queries anymore.`)\n    }\n}\n"], "sourceRoot": ".."}