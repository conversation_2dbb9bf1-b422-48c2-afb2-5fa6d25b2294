{"version": 3, "sources": ["../../src/find-options/operator/Or.ts"], "names": [], "mappings": ";;AAEA,gBAEC;AAJD,kDAA8C;AAE9C,SAAgB,EAAE,CAAI,GAAG,MAAyB;IAC9C,OAAO,IAAI,2BAAY,CAAC,IAAI,EAAE,MAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAC5D,CAAC", "file": "Or.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\nexport function Or<T>(...values: FindOperator<T>[]): FindOperator<T> {\n    return new FindOperator(\"or\", values as any, true, true)\n}\n"], "sourceRoot": "../.."}