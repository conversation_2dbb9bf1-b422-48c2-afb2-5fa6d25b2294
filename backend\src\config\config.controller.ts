import {
  Controller,
  Get,
  Post,
  Body,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from './config.service';

@Controller('config')
export class ConfigController {
  constructor(private readonly configService: ConfigService) {}

  @Get('all')
  async getAllConfigs() {
    return this.configService.getAllConfigs();
  }

  @Get('win-modifier')
  async getWinModifier() {
    const modifier = await this.configService.getWinModifier();
    return { win_modifier: modifier };
  }

  @Post('win-modifier')
  async setWinModifier(@Body() body: { modifier: number }) {
    if (body.modifier === undefined) {
      throw new BadRequestException('Missing modifier value');
    }

    if (body.modifier < -0.3 || body.modifier > 0.3) {
      throw new BadRequestException('Modifier must be between -0.3 and 0.3');
    }

    await this.configService.setWinModifier(body.modifier);
    return { success: true, modifier: body.modifier };
  }

  @Post('set')
  async setConfig(@Body() body: { key: string; value: string }) {
    if (!body.key || body.value === undefined) {
      throw new BadRequestException('Missing key or value');
    }

    const config = await this.configService.setConfig(body.key, body.value);
    return config;
  }
}
