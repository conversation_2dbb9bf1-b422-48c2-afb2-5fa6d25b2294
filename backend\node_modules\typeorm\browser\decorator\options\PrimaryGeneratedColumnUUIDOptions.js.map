{"version": 3, "sources": ["../browser/src/decorator/options/PrimaryGeneratedColumnUUIDOptions.ts"], "names": [], "mappings": "", "file": "PrimaryGeneratedColumnUUIDOptions.js", "sourcesContent": ["/**\n * Describes all options for PrimaryGeneratedColumn decorator with numeric uuid strategy.\n */\nexport interface PrimaryGeneratedColumnUUIDOptions {\n    /**\n     * Column name in the database.\n     */\n    name?: string\n\n    /**\n     * Column comment. Not supported by all database types.\n     */\n    comment?: string\n\n    /**\n     * Name of the primary key constraint.\n     */\n    primaryKeyConstraintName?: string\n}\n"], "sourceRoot": "../.."}