/* App Layout */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
}

/* Header */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 12px 20px;
  backdrop-filter: blur(20px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 400px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.balance {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  font-weight: 600;
  padding: 8px 12px;
  background: var(--glass-bg);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
}

/* Main Content */
.app-main {
  flex: 1;
  padding: 80px 20px 100px;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.page-content {
  min-height: calc(100vh - 180px);
}

/* Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 12px 8px;
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--glass-border);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  min-width: 50px;
}

.nav-item svg {
  font-size: 18px;
  margin-bottom: 2px;
}

.nav-item.active {
  color: var(--primary-green);
  background: rgba(0, 255, 136, 0.1);
  transform: translateY(-2px);
}

.nav-item:hover {
  color: var(--primary-green);
  background: rgba(0, 255, 136, 0.05);
}

/* Loading Screen */
.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 20px;
  text-align: center;
}

.loading-spinner {
  margin-bottom: 20px;
}

/* Responsive */
@media (max-width: 480px) {
  .app-main {
    padding: 70px 16px 90px;
  }

  .header-content {
    padding: 0 4px;
  }

  .logo {
    font-size: 18px;
  }

  .balance {
    font-size: 14px;
    padding: 6px 10px;
  }

  .nav-item {
    padding: 6px 8px;
    font-size: 9px;
    min-width: 45px;
  }

  .nav-item svg {
    font-size: 16px;
  }
}
