import { GameService } from './game.service';
import { CoinflipService } from './coinflip.service';
export declare class GameController {
    private readonly gameService;
    private readonly coinflipService;
    constructor(gameService: GameService, coinflipService: CoinflipService);
    playCrash(body: {
        user_id: string;
        bet_amount: number;
        target_multiplier: number;
    }): Promise<{
        success: boolean;
        crash_point?: number;
        payout?: number;
        seed?: string;
    }>;
    playDouble(body: {
        user_id: string;
        bet_amount: number;
        chosen_multiplier: number;
    }): Promise<{
        success: boolean;
        result_multiplier?: number;
        payout?: number;
        seed?: string;
    }>;
    createCoinflipRoom(body: {
        user_id: string;
        stake: number;
    }): Promise<import("./coinflip-room.entity").CoinflipRoom>;
    joinCoinflipRoom(body: {
        room_id: number;
        user_id: string;
    }): Promise<{
        success: boolean;
        room?: import("./coinflip-room.entity").CoinflipRoom;
        winner?: string;
        result?: string;
    }>;
    getWaitingRooms(): Promise<import("./coinflip-room.entity").CoinflipRoom[]>;
    getRecentGames(): Promise<import("./coinflip-room.entity").CoinflipRoom[]>;
    getUserRooms(user_id: string): Promise<import("./coinflip-room.entity").CoinflipRoom[]>;
    getRoomById(room_id: string): Promise<import("./coinflip-room.entity").CoinflipRoom>;
}
