import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './user.entity';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async createUser(telegram_id: string, username: string, firstname: string): Promise<User> {
    let user = await this.userRepository.findOne({ where: { telegram_id } });
    if (!user) {
      user = this.userRepository.create({
        id: telegram_id,
        telegram_id,
        username,
        firstname,
        balance_ton: 0,
      });
      await this.userRepository.save(user);
    }
    return user;
  }

  async getUser(telegram_id: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { telegram_id } });
  }

  async updateBalance(telegram_id: string, amount: number): Promise<User | null> {
    const user = await this.getUser(telegram_id);
    if (user) {
      user.balance_ton = Number(user.balance_ton) + Number(amount);
      await this.userRepository.save(user);
      return user;
    }
    return null;
  }

  async deductBalance(telegram_id: string, amount: number): Promise<boolean> {
    const user = await this.getUser(telegram_id);
    if (user && Number(user.balance_ton) >= Number(amount)) {
      user.balance_ton = Number(user.balance_ton) - Number(amount);
      await this.userRepository.save(user);
      return true;
    }
    return false;
  }

  async getLeaderboard(): Promise<User[]> {
    return this.userRepository.find({
      order: { balance_ton: 'DESC' },
      take: 10,
      select: ['telegram_id', 'username', 'firstname', 'balance_ton'],
    });
  }

  async getUserById(telegram_id: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { telegram_id } });
  }

  async banUser(telegram_id: string): Promise<boolean> {
    const user = await this.getUser(telegram_id);
    if (user) {
      user.is_banned = true;
      await this.userRepository.save(user);
      return true;
    }
    return false;
  }

  async unbanUser(telegram_id: string): Promise<boolean> {
    const user = await this.getUser(telegram_id);
    if (user) {
      user.is_banned = false;
      await this.userRepository.save(user);
      return true;
    }
    return false;
  }

  async updateWalletAddress(telegram_id: string, wallet_address: string): Promise<User | null> {
    const user = await this.getUser(telegram_id);
    if (user) {
      user.ton_wallet_address = wallet_address;
      await this.userRepository.save(user);
      return user;
    }
    return null;
  }
}
