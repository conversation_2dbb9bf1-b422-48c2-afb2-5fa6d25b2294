import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../users/user.entity';

export enum CoinflipStatus {
  WAITING = 'WAITING',
  PLAYING = 'PLAYING',
  FINISHED = 'FINISHED',
}

@Entity()
export class CoinflipRoom {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  stake: number;

  @ManyToOne(() => User, { nullable: true })
  playerA: User;

  @Column({ nullable: true })
  playerA_id: string;

  @ManyToOne(() => User, { nullable: true })
  playerB: User;

  @Column({ nullable: true })
  playerB_id: string;

  @ManyToOne(() => User, { nullable: true })
  winner: User;

  @Column({ nullable: true })
  winner_id: string;

  @Column({ default: CoinflipStatus.WAITING })
  status: CoinflipStatus;

  @Column({ nullable: true })
  result: string; // heads or tails

  @Column({ nullable: true })
  seed: string; // for provably fair

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
