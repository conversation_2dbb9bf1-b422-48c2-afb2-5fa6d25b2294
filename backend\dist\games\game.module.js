"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const coinflip_room_entity_1 = require("./coinflip-room.entity");
const game_service_1 = require("./game.service");
const coinflip_service_1 = require("./coinflip.service");
const game_controller_1 = require("./game.controller");
const user_module_1 = require("../users/user.module");
const transaction_module_1 = require("../transactions/transaction.module");
const config_module_1 = require("../config/config.module");
let GameModule = class GameModule {
};
exports.GameModule = GameModule;
exports.GameModule = GameModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([coinflip_room_entity_1.CoinflipRoom]),
            user_module_1.UserModule,
            transaction_module_1.TransactionModule,
            config_module_1.ConfigModule,
        ],
        controllers: [game_controller_1.GameController],
        providers: [game_service_1.GameService, coinflip_service_1.CoinflipService],
        exports: [game_service_1.GameService, coinflip_service_1.CoinflipService],
    })
], GameModule);
//# sourceMappingURL=game.module.js.map