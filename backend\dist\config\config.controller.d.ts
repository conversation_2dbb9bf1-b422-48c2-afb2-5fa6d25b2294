import { ConfigService } from './config.service';
export declare class ConfigController {
    private readonly configService;
    constructor(configService: ConfigService);
    getAllConfigs(): Promise<import("./global-config.entity").GlobalConfig[]>;
    getWinModifier(): Promise<{
        win_modifier: number;
    }>;
    setWinModifier(body: {
        modifier: number;
    }): Promise<{
        success: boolean;
        modifier: number;
    }>;
    setConfig(body: {
        key: string;
        value: string;
    }): Promise<import("./global-config.entity").GlobalConfig>;
}
