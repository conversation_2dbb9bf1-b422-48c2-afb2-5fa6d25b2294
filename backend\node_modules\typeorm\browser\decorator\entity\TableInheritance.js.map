{"version": 3, "sources": ["../browser/src/decorator/entity/TableInheritance.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAItD;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,OAGhC;IACG,OAAO,UAAU,MAAgB;QAC7B,sBAAsB,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC;YACvC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;YAC7D,MAAM,EACF,OAAO,IAAI,OAAO,CAAC,MAAM;gBACrB,CAAC,CAAC,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ;oBAChC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE;oBAC1B,CAAC,CAAC,OAAO,CAAC,MAAM;gBACpB,CAAC,CAAC,SAAS;SACK,CAAC,CAAA;IACjC,CAAC,CAAA;AACL,CAAC", "file": "TableInheritance.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { InheritanceMetadataArgs } from \"../../metadata-args/InheritanceMetadataArgs\"\nimport { ColumnOptions } from \"../options/ColumnOptions\"\n\n/**\n * Sets for entity to use table inheritance pattern.\n */\nexport function TableInheritance(options?: {\n    pattern?: \"STI\" /*|\"CTI\"*/\n    column?: string | ColumnOptions\n}): ClassDecorator {\n    return function (target: Function) {\n        getMetadataArgsStorage().inheritances.push({\n            target: target,\n            pattern: options && options.pattern ? options.pattern : \"STI\",\n            column:\n                options && options.column\n                    ? typeof options.column === \"string\"\n                        ? { name: options.column }\n                        : options.column\n                    : undefined,\n        } as InheritanceMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}