{"version": 3, "sources": ["../browser/src/error/UpdateValuesMissingError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,wBAAyB,SAAQ,YAAY;IACtD;QACI,KAAK,CACD,yHAAyH,CAC5H,CAAA;IACL,CAAC;CACJ", "file": "UpdateValuesMissingError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\nexport class UpdateValuesMissingError extends TypeORMError {\n    constructor() {\n        super(\n            `Cannot perform update query because update values are not defined. Call \"qb.set(...)\" method to specify updated values.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}