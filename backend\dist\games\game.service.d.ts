import { UserService } from '../users/user.service';
import { TransactionService } from '../transactions/transaction.service';
import { ConfigService } from '../config/config.service';
export declare class GameService {
    private userService;
    private transactionService;
    private configService;
    constructor(userService: UserService, transactionService: TransactionService, configService: ConfigService);
    getWinModifier(): Promise<number>;
    generateSeed(): string;
    generateRandomNumber(seed: string, min?: number, max?: number): number;
    placeBet(user_id: string, amount: number, game_type: string, game_data?: any): Promise<boolean>;
    payoutWin(user_id: string, amount: number, game_type: string, game_data?: any): Promise<void>;
    playCrash(user_id: string, bet_amount: number, target_multiplier: number): Promise<{
        success: boolean;
        crash_point?: number;
        payout?: number;
        seed?: string;
    }>;
    playDouble(user_id: string, bet_amount: number, chosen_multiplier: number): Promise<{
        success: boolean;
        result_multiplier?: number;
        payout?: number;
        seed?: string;
    }>;
}
