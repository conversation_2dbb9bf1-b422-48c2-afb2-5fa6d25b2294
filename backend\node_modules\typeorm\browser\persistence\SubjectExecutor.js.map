{"version": 3, "sources": ["../browser/src/persistence/SubjectExecutor.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAA;AACrE,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAA;AAC/E,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAA;AACtF,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAA;AAKtF,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAA;AACnE,OAAO,EAAE,wBAAwB,EAAE,MAAM,iCAAiC,CAAA;AAC1E,OAAO,EAAE,sBAAsB,EAAE,MAAM,+BAA+B,CAAA;AACtE,OAAO,EAAE,+BAA+B,EAAE,MAAM,wCAAwC,CAAA;AACxF,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAE3C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AACjD,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AAEzD;;;GAGG;AACH,MAAM,OAAO,eAAe;IAsDxB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACI,WAAwB,EACxB,QAAmB,EACnB,OAAqC;QA5DzC,4EAA4E;QAC5E,oBAAoB;QACpB,4EAA4E;QAE5E;;WAEG;QACH,4BAAuB,GAAY,KAAK,CAAA;QAqBxC;;WAEG;QACO,mBAAc,GAAc,EAAE,CAAA;QAExC;;WAEG;QACO,mBAAc,GAAc,EAAE,CAAA;QAExC;;WAEG;QACO,mBAAc,GAAc,EAAE,CAAA;QAExC;;WAEG;QACO,uBAAkB,GAAc,EAAE,CAAA;QAE5C;;WAEG;QACO,oBAAe,GAAc,EAAE,CAAA;QAWrC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAA;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,QAAQ,EAAE,CAAA;QACf,IAAI,CAAC,SAAS,EAAE,CAAA;IACpB,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,KAAK,CAAC,OAAO;QACT,2CAA2C;QAE3C,gFAAgF;QAChF,IAAI,iBAAiB,GAAkC,SAAS,CAAA;QAChE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YACpD,gDAAgD;YAChD,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;YACtD,IAAI,iBAAiB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;gBACrC,MAAM,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;YACjD,mDAAmD;QACvD,CAAC;QAED,sIAAsI;QACtI,+EAA+E;QAC/E,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YACnD,8BAA8B;YAC9B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;YAC7D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;YAC7D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;YAC7D,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;YACjE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;YAC9D,IAAI,CAAC,SAAS,EAAE,CAAA;YAChB,iCAAiC;QACrC,CAAC;QAED,6GAA6G;QAE7G,8BAA8B;QAE9B,gCAAgC;QAChC,8BAA8B;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,wBAAwB,CAC9C,IAAI,CAAC,cAAc,CACtB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAChB,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACpC,iCAAiC;QAEjC,qFAAqF;QACrF,sEAAsE;QACtE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CACzC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,aAAa,CACrC,CAAA;QAED,4BAA4B;QAC5B,6BAA6B;QAC7B,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACpC,gCAAgC;QAEhC,yHAAyH;QACzH,4BAA4B;QAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,wBAAwB,CAC9C,IAAI,CAAC,cAAc,CACtB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAChB,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACpC,+BAA+B;QAE/B,mCAAmC;QACnC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAC7C,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,iBAAiB,CACzC,CAAA;QAED,iCAAiC;QACjC,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAExC,+BAA+B;QAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAC1C,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CACvC,CAAA;QAED,6BAA6B;QAC7B,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAA;QAErC,6GAA6G;QAC7G,4DAA4D;QAC5D,IAAI,CAAC,uCAAuC,EAAE,CAAA;QAC9C,+DAA+D;QAE/D,uFAAuF;QACvF,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YACpD,+CAA+C;YAC/C,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAA;YACrD,IAAI,iBAAiB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;gBACrC,MAAM,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;YACjD,kDAAkD;QACtD,CAAC;QACD,8CAA8C;IAClD,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,QAAQ;QACd,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa;gBAC9C,MAAM,IAAI,6BAA6B,CAAC,OAAO,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,SAAS;QACf,IAAI,6BAA6B,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC7D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CACzC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,cAAc,CACtC,CAAA;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CACzC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,aAAa,CACrC,CAAA;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CACzC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,aAAa,CACrC,CAAA;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAC7C,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,iBAAiB,CACzC,CAAA;QACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAC1C,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CACvC,CAAA;QACD,IAAI,CAAC,uBAAuB;YACxB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;gBAC9B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;gBAC9B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;gBAC9B,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC;gBAClC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACO,2BAA2B;QACjC,MAAM,MAAM,GAAG,IAAI,iBAAiB,EAAE,CAAA;QACtC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACpC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,0BAA0B,CACnD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,CAClB,CACJ,CAAA;QACL,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACpC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,0BAA0B,CACnD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,aAAa,CACxB,CACJ,CAAA;QACL,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACpC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,0BAA0B,CACnD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,UAAU,CACrB,CACJ,CAAA;QACL,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM;YAC9B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACxC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,8BAA8B,CACvD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,UAAU,CACrB,CACJ,CAAA;QACL,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM;YAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACrC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,2BAA2B,CACpD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,UAAU,CACrB,CACJ,CAAA;QACL,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;;;OAIG;IACO,0BAA0B;QAChC,MAAM,MAAM,GAAG,IAAI,iBAAiB,EAAE,CAAA;QACtC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACpC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,yBAAyB,CAClD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,UAAU,CACrB,CACJ,CAAA;QACL,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACpC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,yBAAyB,CAClD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,aAAa,CACxB,CACJ,CAAA;QACL,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACpC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,yBAAyB,CAClD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,UAAU,CACrB,CACJ,CAAA;QACL,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM;YAC9B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACxC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,6BAA6B,CACtD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,UAAU,CACrB,CACJ,CAAA;QACL,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM;YAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CACrC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,0BAA0B,CACnD,MAAM,EACN,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,MAAO,EACf,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,UAAU,CACrB,CACJ,CAAA;QACL,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB;QACnC,mDAAmD;QACnD,MAAM,CAAC,qBAAqB,EAAE,wBAAwB,CAAC,GACnD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAA;QAEzD,qGAAqG;QACrG,KAAK,MAAM,SAAS,IAAI,wBAAwB,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,qBAAqB,CAAC,SAAS,CAAC,CAAA;YAEjD,8EAA8E;YAC9E,wFAAwF;YACxF,MAAM,cAAc,GAAoB,EAAE,CAAA;YAC1C,MAAM,kBAAkB,GAAc,EAAE,CAAA;YACxC,MAAM,oBAAoB,GAAc,EAAE,CAAA;YAC1C,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAChE,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACtD,OAAO,CAAC,MAAM,CACV,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,GAAG,IAAI,IAAI,EAAE,CAAA;oBAClB,CAAC;oBAED,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACtD,OAAO,CAAC,MAAM,CACV,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,GAAG,IAAI,IAAI,EAAE,CAAA;oBAClB,CAAC;oBAED,OAAO,CAAC,6BAA6B,EAAE,CAAA;oBAEvC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;oBAChC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,MAAO,CAAC,CAAA;gBACxC,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,IACH,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EAC9D,CAAC;gBACC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACzB,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACtC,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,CAAC;gBACJ,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACzB,+CAA+C;oBAC/C,6IAA6I;oBAC7I,iGAAiG;oBACjG,mEAAmE;oBACnE,IACI,OAAO,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC;wBAC/B,OAAO,CAAC,QAAQ,CAAC,QAAQ;wBACzB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;4BAC3C,QAAQ;wBACZ,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;4BAC3C,KAAK,EACX,CAAC;wBACC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;oBACtC,CAAC;yBAAM,CAAC;wBACJ,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;wBAChC,cAAc,CAAC,IAAI,CACf,OAAO,CAAC,6BAA6B,EAAE,CAC1C,CAAA;oBACL,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC;YAED,sDAAsD;YACtD,IACI,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAChE,CAAC;gBACC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CACtD,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,EAC3B,cAAc,CACjB,CAAA;gBACD,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oBAChC,OAAO,CAAC,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;oBACpD,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;oBACxD,OAAO,CAAC,gBAAgB,GAAG,cAAc,CAAC,KAAK,CAAC,CAAA;gBACpD,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,CAAC;gBACJ,sCAAsC;gBACtC,mFAAmF;gBACnF,yGAAyG;gBACzG,kFAAkF;gBAClF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;yBAC9C,kBAAkB,EAAE;yBACpB,MAAM,EAAE;yBACR,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;yBACjC,MAAM,CAAC,cAAc,CAAC;yBACtB,YAAY,CACT,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK;wBACzC,CAAC,CAAC,KAAK;wBACP,CAAC,CAAC,IAAI,CACb;yBACA,aAAa,CAAC,KAAK,CAAC;yBACpB,OAAO,EAAE,CAAA;oBAEd,kBAAkB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wBAC1C,OAAO,CAAC,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;wBACpD,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;wBACxD,OAAO,CAAC,gBAAgB,GAAG,cAAc,CAAC,KAAK,CAAC,CAAA;oBACpD,CAAC,CAAC,CAAA;gBACN,CAAC;gBAED,mFAAmF;gBACnF,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAClC,KAAK,MAAM,OAAO,IAAI,oBAAoB,EAAE,CAAC;wBACzC,OAAO,CAAC,gBAAgB;4BACpB,OAAO,CAAC,6BAA6B,EAAE,CAAA,CAAC,uEAAuE;wBAEnH,+CAA+C;wBAC/C,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,KAAK,YAAY;4BAC1C,MAAM,IAAI,wBAAwB,CAC9B,IAAI,CAAC,WAAW,CACnB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;wBAErB,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;6BACzB,kBAAkB,EAAE;6BACpB,MAAM,EAAE;6BACR,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;6BAC7B,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC;6BAChC,YAAY,CACT,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK;4BACzC,CAAC,CAAC,KAAK;4BACP,CAAC,CAAC,IAAI,CACb;6BACA,aAAa,CAAC,KAAK,CAAC;6BACpB,OAAO,EAAE;6BACT,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE;4BACnB,OAAO,CAAC,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;4BAChD,OAAO,CAAC,YAAY;gCAChB,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;wBACrC,CAAC,CAAC,CAAA;wBAEN,gDAAgD;wBAChD,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,KAAK,eAAe,EAAE,CAAC;4BAChD,MAAM,IAAI,sBAAsB,CAC5B,IAAI,CAAC,WAAW,CACnB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;wBACrB,CAAC;6BAAM,IACH,OAAO,CAAC,QAAQ,CAAC,QAAQ,KAAK,mBAAmB,EACnD,CAAC;4BACC,MAAM,IAAI,+BAA+B,CACrC,IAAI,CAAC,WAAW,CACnB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;wBACrB,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBACzB,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;oBACvB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBACxC,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAC/B,OAAO,CAAC,YAAa,CACxB,CAAA;wBACD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;4BACxC,MAAM,aAAa,GACf,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACnD,KAAK,EACL,MAAM,CACT,CAAA;4BACL,MAAM,CAAC,cAAc,CACjB,OAAO,CAAC,YAAa,EACrB,aAAa,CAChB,CAAA;wBACL,CAAC;oBACL,CAAC,CAAC,CAAA;gBACN,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB;QACnC,MAAM,aAAa,GAAG,KAAK,EAAE,OAAgB,EAAE,EAAE;YAC7C,IAAI,CAAC,OAAO,CAAC,UAAU;gBACnB,MAAM,IAAI,6BAA6B,CAAC,OAAO,CAAC,CAAA;YAEpD,qDAAqD;YACrD,IACI,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAChE,CAAC;gBACC,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA;gBAC3D,IACI,OAAO,CAAC,QAAQ,CAAC,cAAc;oBAC/B,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAC9C,CAAC;oBACC,OAAO,aAAa,CAChB,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAC/C,CAAA;gBACL,CAAC;gBAED,IACI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;oBACjC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAChD,CAAC;oBACC,OAAO,aAAa,CAChB,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,CAAA;gBACL,CAAC;gBAED,IACI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;oBACjC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAChD,CAAC;oBACC,aAAa,CACT,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,GAAG,IAAI,IAAI,EAAE,CAAA;gBAClB,CAAC;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAA6B,CAAA;gBAE9D,MAAM,OAAO,CAAC,MAAM,CAChB,OAAO,CAAC,QAAQ,CAAC,MAAM,EACvB,OAAO,CAAC,UAAU,EAClB,aAAa,CAChB,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,SAAS,GACX,OAAO,CAAC,6BAA6B,EAAE,CAAA;gBAE3C,gDAAgD;gBAChD,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBAChC,KAAK,YAAY;wBACb,MAAM,IAAI,wBAAwB,CAC9B,IAAI,CAAC,WAAW,CACnB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;wBACjB,MAAK;oBAET,KAAK,eAAe;wBAChB,MAAM,IAAI,sBAAsB,CAC5B,IAAI,CAAC,WAAW,CACnB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;wBACjB,MAAK;oBAET,KAAK,mBAAmB;wBACpB,MAAM,IAAI,+BAA+B,CACrC,IAAI,CAAC,WAAW,CACnB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;wBACjB,MAAK;gBACb,CAAC;gBAED,qCAAqC;gBACrC,2EAA2E;gBAC3E,yGAAyG;gBACzG,kFAAkF;gBAClF,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;qBAC9C,kBAAkB,EAAE;qBACpB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;qBAC/B,GAAG,CAAC,SAAS,CAAC;qBACd,YAAY,CACT,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK;oBACzC,CAAC,CAAC,KAAK;oBACP,CAAC,CAAC,IAAI,CACb;qBACA,aAAa,CAAC,KAAK,CAAC,CAAA;gBAEzB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACjB,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBACtD,CAAC;qBAAM,CAAC;oBACJ,iEAAiE;oBACjE,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBAChD,CAAC;gBAED,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAAC,OAAO,EAAE,CAAA;gBACvD,MAAM,kBAAkB,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;gBACxD,IAAI,kBAAkB,EAAE,CAAC;oBACrB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBACxC,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,kBAAmB,CAAC,CAAA;wBACxD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;4BACxC,MAAM,aAAa,GACf,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACnD,KAAK,EACL,MAAM,CACT,CAAA;4BACL,MAAM,CAAC,cAAc,CACjB,kBAAmB,EACnB,aAAa,CAChB,CAAA;wBACL,CAAC;oBACL,CAAC,CAAC,CAAA;oBACF,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;wBACxB,OAAO,CAAC,YAAY,GAAG,EAAE,CAAA;oBAC7B,CAAC;oBACD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAA;gBAC3D,CAAC;YACL,CAAC;QACL,CAAC,CAAA;QAED,4CAA4C;QAC5C,6FAA6F;QAC7F,MAAM,iBAAiB,GAAc,EAAE,CAAA;QACvC,MAAM,iBAAiB,GAAc,EAAE,CAAA;QAEvC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC7C,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACnC,CAAC;iBAAM,CAAC;gBACJ,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACnC,CAAC;QACL,CAAC;QAED,oCAAoC;QACpC,MAAM,gBAAgB,GAAG,IAAI,OAAO,CAAO,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;YAC1D,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;gBACtC,IAAI,CAAC;oBACD,MAAM,aAAa,CAAC,OAAO,CAAC,CAAA;gBAChC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,KAAK,CAAC,CAAA;gBACf,CAAC;YACL,CAAC;YACD,EAAE,EAAE,CAAA;QACR,CAAC,CAAC,CAAA;QAEF,yCAAyC;QACzC,MAAM,OAAO,CAAC,GAAG,CAAC;YACd,GAAG,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC;YACvC,gBAAgB;SACnB,CAAC,CAAA;IACN,CAAC;IAED;;;;OAIG;IACO,KAAK,CAAC,uBAAuB;QACnC,mDAAmD;QACnD,MAAM,CAAC,qBAAqB,EAAE,wBAAwB,CAAC,GACnD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAA;QAEzD,KAAK,MAAM,SAAS,IAAI,wBAAwB,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,qBAAqB,CAAC,SAAS,CAAC,CAAA;YACjD,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;gBACxC,IAAI,CAAC,OAAO,CAAC,UAAU;oBACnB,MAAM,IAAI,6BAA6B,CAAC,OAAO,CAAC,CAAA;gBAEpD,OAAO,OAAO,CAAC,UAAU,CAAA;YAC7B,CAAC,CAAC,CAAA;YAEF,qDAAqD;YACrD,IACI,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAChE,CAAC;gBACC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAA6B,CAAA;gBAC9D,MAAM,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;YACjE,CAAC;iBAAM,CAAC;gBACJ,gDAAgD;gBAChD,QAAQ,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBACpC,KAAK,YAAY;wBACb,MAAM,IAAI,wBAAwB,CAC9B,IAAI,CAAC,WAAW,CACnB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;wBAClB,MAAK;oBAET,KAAK,eAAe;wBAChB,MAAM,IAAI,sBAAsB,CAC5B,IAAI,CAAC,WAAW,CACnB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;wBAClB,MAAK;gBACb,CAAC;gBAED,qCAAqC;gBACrC,qGAAqG;gBACrG,uGAAuG;gBACvG,kFAAkF;gBAClF,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;qBACzB,kBAAkB,EAAE;qBACpB,MAAM,EAAE;qBACR,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;qBACjC,KAAK,CAAC,UAAU,CAAC;qBACjB,aAAa,CAAC,KAAK,CAAC;qBACpB,OAAO,EAAE,CAAA;YAClB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,OAAgB;QAC5C,MAAM,MAAM,GAAkB,EAAE,CAAA;QAEhC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC5C,QAAQ,CAAC,SAAS,CACd,MAAM,EACN,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAC3C,CAAA;YACL,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,2BAA2B;QACvC,MAAM,OAAO,CAAC,GAAG,CACb,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC1C,IAAI,CAAC,OAAO,CAAC,UAAU;gBACnB,MAAM,IAAI,6BAA6B,CAAC,OAAO,CAAC,CAAA;YAEpD,IAAI,YAA0B,CAAA;YAE9B,qDAAqD;YACrD,IACI,eAAe,CAAC,oBAAoB,CAChC,IAAI,CAAC,WAAW,CAAC,OAAO,CAC3B,EACH,CAAC;gBACC,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA;gBAC3D,IACI,OAAO,CAAC,QAAQ,CAAC,cAAc;oBAC/B,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAC9C,CAAC;oBACC,OAAO,aAAa,CAChB,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAC/C,CAAA;gBACL,CAAC;gBAED,IACI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;oBACjC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAChD,CAAC;oBACC,OAAO,aAAa,CAChB,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,CAAA;gBACL,CAAC;gBAED,IACI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;oBACjC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAChD,CAAC;oBACC,aAAa,CACT,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,GAAG,IAAI,IAAI,EAAE,CAAA;gBAClB,CAAC;gBAED,IACI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;oBACjC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAChD,CAAC;oBACC,aAAa,CACT,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,GAAG,IAAI,IAAI,EAAE,CAAA;gBAClB,CAAC;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW;qBAC3B,OAA6B,CAAA;gBAElC,YAAY,GAAG,MAAM,OAAO,CAAC,MAAM,CAC/B,OAAO,CAAC,QAAQ,CAAC,MAAM,EACvB,OAAO,CAAC,UAAU,EAClB,aAAa,CAChB,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,0CAA0C;gBAC1C,gFAAgF;gBAChF,yGAAyG;gBACzG,kFAAkF;gBAClF,MAAM,sBAAsB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;qBAClD,kBAAkB,EAAE;qBACpB,UAAU,EAAE;qBACZ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;qBAC7B,YAAY,CACT,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK;oBACzC,CAAC,CAAC,KAAK;oBACP,CAAC,CAAC,IAAI,CACb;qBACA,aAAa,CAAC,KAAK,CAAC,CAAA;gBAEzB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACjB,sBAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBAC1D,CAAC;qBAAM,CAAC;oBACJ,iEAAiE;oBACjE,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBACpD,CAAC;gBAED,YAAY,GAAG,MAAM,sBAAsB,CAAC,OAAO,EAAE,CAAA;YACzD,CAAC;YAED,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;YACpD,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACvB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxC,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAC/B,OAAO,CAAC,YAAa,CACxB,CAAA;oBACD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;wBACxC,MAAM,aAAa,GACf,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACnD,KAAK,EACL,MAAM,CACT,CAAA;wBACL,MAAM,CAAC,cAAc,CACjB,OAAO,CAAC,YAAa,EACrB,aAAa,CAChB,CAAA;oBACL,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC;YAED,+EAA+E;YAC/E,gDAAgD;YAChD,mFAAmF;YACnF,8DAA8D;YAC9D,uDAAuD;YACvD,EAAE;YACF,+DAA+D;YAC/D,4FAA4F;YAC5F,YAAY;YACZ,WAAW;YACX,IAAI;QACR,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACpC,MAAM,OAAO,CAAC,GAAG,CACb,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACvC,IAAI,CAAC,OAAO,CAAC,UAAU;gBACnB,MAAM,IAAI,6BAA6B,CAAC,OAAO,CAAC,CAAA;YAEpD,IAAI,YAA0B,CAAA;YAE9B,qDAAqD;YACrD,IACI,eAAe,CAAC,oBAAoB,CAChC,IAAI,CAAC,WAAW,CAAC,OAAO,CAC3B,EACH,CAAC;gBACC,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA;gBAC3D,IACI,OAAO,CAAC,QAAQ,CAAC,cAAc;oBAC/B,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAC9C,CAAC;oBACC,OAAO,aAAa,CAChB,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAC/C,CAAA;gBACL,CAAC;gBAED,IACI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;oBACjC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAChD,CAAC;oBACC,OAAO,aAAa,CAChB,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,CAAA;gBACL,CAAC;gBAED,IACI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;oBACjC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAChD,CAAC;oBACC,aAAa,CACT,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,GAAG,IAAI,IAAI,EAAE,CAAA;gBAClB,CAAC;gBAED,IACI,OAAO,CAAC,QAAQ,CAAC,gBAAgB;oBACjC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAChD,CAAC;oBACC,aAAa,CACT,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CACjD,GAAG,IAAI,CAAA;gBACZ,CAAC;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW;qBAC3B,OAA6B,CAAA;gBAElC,YAAY,GAAG,MAAM,OAAO,CAAC,MAAM,CAC/B,OAAO,CAAC,QAAQ,CAAC,MAAM,EACvB,OAAO,CAAC,UAAU,EAClB,aAAa,CAChB,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,oCAAoC;gBACpC,0EAA0E;gBAC1E,yGAAyG;gBACzG,kFAAkF;gBAClF,MAAM,sBAAsB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO;qBAClD,kBAAkB,EAAE;qBACpB,OAAO,EAAE;qBACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;qBAC7B,YAAY,CACT,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK;oBACzC,CAAC,CAAC,KAAK;oBACP,CAAC,CAAC,IAAI,CACb;qBACA,aAAa,CAAC,KAAK,CAAC,CAAA;gBAEzB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACjB,sBAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBAC1D,CAAC;qBAAM,CAAC;oBACJ,iEAAiE;oBACjE,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;gBACpD,CAAC;gBAED,YAAY,GAAG,MAAM,sBAAsB,CAAC,OAAO,EAAE,CAAA;YACzD,CAAC;YAED,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;YACpD,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACvB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxC,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAC/B,OAAO,CAAC,YAAa,CACxB,CAAA;oBACD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;wBACxC,MAAM,aAAa,GACf,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACnD,KAAK,EACL,MAAM,CACT,CAAA;wBACL,MAAM,CAAC,cAAc,CACjB,OAAO,CAAC,YAAa,EACrB,aAAa,CAChB,CAAA;oBACL,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC;YAED,+EAA+E;YAC/E,gDAAgD;YAChD,mFAAmF;YACnF,8DAA8D;YAC9D,uDAAuD;YACvD,EAAE;YACF,+DAA+D;YAC/D,4FAA4F;YAC5F,YAAY;YACZ,WAAW;YACX,IAAI;QACR,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;;OAGG;IACO,uCAAuC;QAC7C,oCAAoC;QACpC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B,IAAI,CAAC,gDAAgD,CACjD,IAAI,CAAC,cAAc,CACtB,CAAA;QAEL,mCAAmC;QACnC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;YAC1B,IAAI,CAAC,gDAAgD,CACjD,IAAI,CAAC,cAAc,CACtB,CAAA;QAEL,wCAAwC;QACxC,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM;YAC9B,IAAI,CAAC,gDAAgD,CACjD,IAAI,CAAC,kBAAkB,CAC1B,CAAA;QAEL,qCAAqC;QACrC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM;YAC3B,IAAI,CAAC,gDAAgD,CACjD,IAAI,CAAC,eAAe,CACvB,CAAA;QAEL,iDAAiD;QACjD,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YAC7B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBACpC,IAAI,CAAC,OAAO,CAAC,MAAM;oBAAE,OAAM;gBAE3B,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;oBACtD,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,MAAO,EAAE,SAAS,CAAC,CAAA;gBAC5D,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;QACN,CAAC;QAED,+BAA+B;QAC/B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,MAAM;gBAAE,OAAM;YAE3B,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAChD,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAO,CAAC,CAAA;YACxC,CAAC,CAAC,CAAA;YAEF,mBAAmB;YACnB,IACI,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAChE,CAAC;gBACC,IACI,OAAO,CAAC,QAAQ,CAAC,cAAc;oBAC/B,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY;oBAC5C,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY;wBACxC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAClD,CAAC;oBACC,OAAO,OAAO,CAAC,MAAM,CACjB,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAC/C,CAAA;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;OAGG;IACO,gDAAgD,CACtD,QAAmB;QAEnB,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,MAAM;gBAAE,OAAM;YAE3B,qEAAqE;YACrE,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACxC,2EAA2E;gBAC3E,IACI,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC;oBAChD,OAAO,CAAC,QAAQ,CAAC,oBAAoB;yBAChC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;yBAClC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAElC,OAAM;gBAEV,yCAAyC;gBACzC,IAAI,MAAM,CAAC,SAAS;oBAAE,OAAM;gBAE5B,yBAAyB;gBACzB,IAAI,MAAM,CAAC,YAAY;oBAAE,OAAM;gBAE/B,0BAA0B;gBAC1B,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;oBACpB,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,MAAO,CAAC,CAAA;oBAC1D,IAAI,WAAW,KAAK,SAAS;wBACzB,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,MAAO,EAAE,IAAI,CAAC,CAAA;gBACpD,CAAC;gBAED,4BAA4B;gBAC5B,IAAI,OAAO,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAC/B,CAAC,kBAAkB,EAAE,EAAE;wBACnB,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAC3C,CAAC,MAAM,EAAE,EAAE;4BACP,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI;gCAAE,OAAM;4BAErC,MAAM,CAAC,cAAc,CACjB,OAAO,CAAC,MAAO,EACf,WAAW,CAAC,QAAQ,CAChB,kBAAkB,CAAC,KAAK,CAC3B;gCACG,CAAC,CAAC,MAAM,CAAC,gBAAiB,CAAC,cAAc,CACnC,kBAAkB,CAAC,KAAK,CAC3B;gCACH,CAAC,CAAC,kBAAkB,CAAC,KAAK,CACjC,CAAA;wBACL,CAAC,CACJ,CAAA;oBACL,CAAC,CACJ,CAAA;gBACL,CAAC;YACL,CAAC,CAAC,CAAA;YAEF,gEAAgE;YAChE,IAAI,OAAO,CAAC,YAAY;gBACpB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAC1B,OAAO,CAAC,QAAQ,CAAC,MAAa,EAC9B,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,YAAY,CACvB,CAAA;QACT,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;;;;;;;OAUG;IACO,iBAAiB,CACvB,QAAmB,EACnB,IAAyB;QAEzB,MAAM,KAAK,GAAiC,EAAE,CAAA;QAC9C,MAAM,IAAI,GAAa,EAAE,CAAA;QACzB,MAAM,yBAAyB,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YACxD,OAAO,OAAO,CAAC,QAAQ,CAAC,4BAA4B,EAAE,CAAC,MAAM,GAAG,CAAC,CAAA;QACrE,CAAC,CAAC,CAAA;QACF,MAAM,eAAe,GACjB,IAAI,KAAK,QAAQ;YACjB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,uBAAuB,CACtD,QAAQ,CACX;YACD,yBAAyB,KAAK,KAAK,CAAA;QAEvC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YAChC,MAAM,GAAG,GACL,eAAe,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU;gBAC1C,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI;gBACvB,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,CAAA;YAC7C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBACd,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;gBACtB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAClB,CAAC;iBAAM,CAAC;gBACJ,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC5B,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IACxB,CAAC;CACJ", "file": "SubjectExecutor.js", "sourcesContent": ["import { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { Subject } from \"./Subject\"\nimport { SubjectTopologicalSorter } from \"./SubjectTopologicalSorter\"\nimport { SubjectChangedColumnsComputer } from \"./SubjectChangedColumnsComputer\"\nimport { SubjectWithoutIdentifierError } from \"../error/SubjectWithoutIdentifierError\"\nimport { SubjectRemovedAndUpdatedError } from \"../error/SubjectRemovedAndUpdatedError\"\nimport { MongoEntityManager } from \"../entity-manager/MongoEntityManager\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { SaveOptions } from \"../repository/SaveOptions\"\nimport { RemoveOptions } from \"../repository/RemoveOptions\"\nimport { BroadcasterResult } from \"../subscriber/BroadcasterResult\"\nimport { NestedSetSubjectExecutor } from \"./tree/NestedSetSubjectExecutor\"\nimport { ClosureSubjectExecutor } from \"./tree/ClosureSubjectExecutor\"\nimport { MaterializedPathSubjectExecutor } from \"./tree/MaterializedPathSubjectExecutor\"\nimport { OrmUtils } from \"../util/OrmUtils\"\nimport { UpdateResult } from \"../query-builder/result/UpdateResult\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Executes all database operations (inserts, updated, deletes) that must be executed\n * with given persistence subjects.\n */\nexport class SubjectExecutor {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Indicates if executor has any operations to execute (e.g. has insert / update / delete operations to be executed).\n     */\n    hasExecutableOperations: boolean = false\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * QueryRunner used to execute all queries with a given subjects.\n     */\n    protected queryRunner: QueryRunner\n\n    /**\n     * Persistence options.\n     */\n    protected options?: SaveOptions & RemoveOptions\n\n    /**\n     * All subjects that needs to be operated.\n     */\n    protected allSubjects: Subject[]\n\n    /**\n     * Subjects that must be inserted.\n     */\n    protected insertSubjects: Subject[] = []\n\n    /**\n     * Subjects that must be updated.\n     */\n    protected updateSubjects: Subject[] = []\n\n    /**\n     * Subjects that must be removed.\n     */\n    protected removeSubjects: Subject[] = []\n\n    /**\n     * Subjects that must be soft-removed.\n     */\n    protected softRemoveSubjects: Subject[] = []\n\n    /**\n     * Subjects that must be recovered.\n     */\n    protected recoverSubjects: Subject[] = []\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        queryRunner: QueryRunner,\n        subjects: Subject[],\n        options?: SaveOptions & RemoveOptions,\n    ) {\n        this.queryRunner = queryRunner\n        this.allSubjects = subjects\n        this.options = options\n        this.validate()\n        this.recompute()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Executes all operations over given array of subjects.\n     * Executes queries using given query runner.\n     */\n    async execute(): Promise<void> {\n        // console.time(\"SubjectExecutor.execute\");\n\n        // broadcast \"before\" events before we start insert / update / remove operations\n        let broadcasterResult: BroadcasterResult | undefined = undefined\n        if (!this.options || this.options.listeners !== false) {\n            // console.time(\".broadcastBeforeEventsForAll\");\n            broadcasterResult = this.broadcastBeforeEventsForAll()\n            if (broadcasterResult.promises.length > 0)\n                await Promise.all(broadcasterResult.promises)\n            // console.timeEnd(\".broadcastBeforeEventsForAll\");\n        }\n\n        // since event listeners and subscribers can call save methods and/or trigger entity changes we need to recompute operational subjects\n        // recompute only in the case if any listener or subscriber was really executed\n        if (broadcasterResult && broadcasterResult.count > 0) {\n            // console.time(\".recompute\");\n            this.insertSubjects.forEach((subject) => subject.recompute())\n            this.updateSubjects.forEach((subject) => subject.recompute())\n            this.removeSubjects.forEach((subject) => subject.recompute())\n            this.softRemoveSubjects.forEach((subject) => subject.recompute())\n            this.recoverSubjects.forEach((subject) => subject.recompute())\n            this.recompute()\n            // console.timeEnd(\".recompute\");\n        }\n\n        // make sure our insert subjects are sorted (using topological sorting) to make cascade inserts work properly\n\n        // console.timeEnd(\"prepare\");\n\n        // execute all insert operations\n        // console.time(\".insertion\");\n        this.insertSubjects = new SubjectTopologicalSorter(\n            this.insertSubjects,\n        ).sort(\"insert\")\n        await this.executeInsertOperations()\n        // console.timeEnd(\".insertion\");\n\n        // recompute update operations since insertion can create updation operations for the\n        // properties it wasn't able to handle on its own (referenced columns)\n        this.updateSubjects = this.allSubjects.filter(\n            (subject) => subject.mustBeUpdated,\n        )\n\n        // execute update operations\n        // console.time(\".updation\");\n        await this.executeUpdateOperations()\n        // console.timeEnd(\".updation\");\n\n        // make sure our remove subjects are sorted (using topological sorting) when multiple entities are passed for the removal\n        // console.time(\".removal\");\n        this.removeSubjects = new SubjectTopologicalSorter(\n            this.removeSubjects,\n        ).sort(\"delete\")\n        await this.executeRemoveOperations()\n        // console.timeEnd(\".removal\");\n\n        // recompute soft-remove operations\n        this.softRemoveSubjects = this.allSubjects.filter(\n            (subject) => subject.mustBeSoftRemoved,\n        )\n\n        // execute soft-remove operations\n        await this.executeSoftRemoveOperations()\n\n        // recompute recover operations\n        this.recoverSubjects = this.allSubjects.filter(\n            (subject) => subject.mustBeRecovered,\n        )\n\n        // execute recover operations\n        await this.executeRecoverOperations()\n\n        // update all special columns in persisted entities, like inserted id or remove ids from the removed entities\n        // console.time(\".updateSpecialColumnsInPersistedEntities\");\n        this.updateSpecialColumnsInPersistedEntities()\n        // console.timeEnd(\".updateSpecialColumnsInPersistedEntities\");\n\n        // finally broadcast \"after\" events after we finish insert / update / remove operations\n        if (!this.options || this.options.listeners !== false) {\n            // console.time(\".broadcastAfterEventsForAll\");\n            broadcasterResult = this.broadcastAfterEventsForAll()\n            if (broadcasterResult.promises.length > 0)\n                await Promise.all(broadcasterResult.promises)\n            // console.timeEnd(\".broadcastAfterEventsForAll\");\n        }\n        // console.timeEnd(\"SubjectExecutor.execute\");\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Validates all given subjects.\n     */\n    protected validate() {\n        this.allSubjects.forEach((subject) => {\n            if (subject.mustBeUpdated && subject.mustBeRemoved)\n                throw new SubjectRemovedAndUpdatedError(subject)\n        })\n    }\n\n    /**\n     * Performs entity re-computations - finds changed columns, re-builds insert/update/remove subjects.\n     */\n    protected recompute(): void {\n        new SubjectChangedColumnsComputer().compute(this.allSubjects)\n        this.insertSubjects = this.allSubjects.filter(\n            (subject) => subject.mustBeInserted,\n        )\n        this.updateSubjects = this.allSubjects.filter(\n            (subject) => subject.mustBeUpdated,\n        )\n        this.removeSubjects = this.allSubjects.filter(\n            (subject) => subject.mustBeRemoved,\n        )\n        this.softRemoveSubjects = this.allSubjects.filter(\n            (subject) => subject.mustBeSoftRemoved,\n        )\n        this.recoverSubjects = this.allSubjects.filter(\n            (subject) => subject.mustBeRecovered,\n        )\n        this.hasExecutableOperations =\n            this.insertSubjects.length > 0 ||\n            this.updateSubjects.length > 0 ||\n            this.removeSubjects.length > 0 ||\n            this.softRemoveSubjects.length > 0 ||\n            this.recoverSubjects.length > 0\n    }\n\n    /**\n     * Broadcasts \"BEFORE_INSERT\", \"BEFORE_UPDATE\", \"BEFORE_REMOVE\", \"BEFORE_SOFT_REMOVE\", \"BEFORE_RECOVER\" events for all given subjects.\n     */\n    protected broadcastBeforeEventsForAll(): BroadcasterResult {\n        const result = new BroadcasterResult()\n        if (this.insertSubjects.length)\n            this.insertSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastBeforeInsertEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                ),\n            )\n        if (this.updateSubjects.length)\n            this.updateSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastBeforeUpdateEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                    subject.databaseEntity,\n                    subject.diffColumns,\n                    subject.diffRelations,\n                ),\n            )\n        if (this.removeSubjects.length)\n            this.removeSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastBeforeRemoveEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                    subject.databaseEntity,\n                    subject.identifier,\n                ),\n            )\n        if (this.softRemoveSubjects.length)\n            this.softRemoveSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastBeforeSoftRemoveEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                    subject.databaseEntity,\n                    subject.identifier,\n                ),\n            )\n        if (this.recoverSubjects.length)\n            this.recoverSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastBeforeRecoverEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                    subject.databaseEntity,\n                    subject.identifier,\n                ),\n            )\n        return result\n    }\n\n    /**\n     * Broadcasts \"AFTER_INSERT\", \"AFTER_UPDATE\", \"AFTER_REMOVE\", \"AFTER_SOFT_REMOVE\", \"AFTER_RECOVER\" events for all given subjects.\n     * Returns void if there wasn't any listener or subscriber executed.\n     * Note: this method has a performance-optimized code organization.\n     */\n    protected broadcastAfterEventsForAll(): BroadcasterResult {\n        const result = new BroadcasterResult()\n        if (this.insertSubjects.length)\n            this.insertSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastAfterInsertEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                    subject.identifier,\n                ),\n            )\n        if (this.updateSubjects.length)\n            this.updateSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastAfterUpdateEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                    subject.databaseEntity,\n                    subject.diffColumns,\n                    subject.diffRelations,\n                ),\n            )\n        if (this.removeSubjects.length)\n            this.removeSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastAfterRemoveEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                    subject.databaseEntity,\n                    subject.identifier,\n                ),\n            )\n        if (this.softRemoveSubjects.length)\n            this.softRemoveSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastAfterSoftRemoveEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                    subject.databaseEntity,\n                    subject.identifier,\n                ),\n            )\n        if (this.recoverSubjects.length)\n            this.recoverSubjects.forEach((subject) =>\n                this.queryRunner.broadcaster.broadcastAfterRecoverEvent(\n                    result,\n                    subject.metadata,\n                    subject.entity!,\n                    subject.databaseEntity,\n                    subject.identifier,\n                ),\n            )\n        return result\n    }\n\n    /**\n     * Executes insert operations.\n     */\n    protected async executeInsertOperations(): Promise<void> {\n        // group insertion subjects to make bulk insertions\n        const [groupedInsertSubjects, groupedInsertSubjectKeys] =\n            this.groupBulkSubjects(this.insertSubjects, \"insert\")\n\n        // then we run insertion in the sequential order which is important since we have an ordered subjects\n        for (const groupName of groupedInsertSubjectKeys) {\n            const subjects = groupedInsertSubjects[groupName]\n\n            // we must separately insert entities which does not have any values to insert\n            // because its not possible to insert multiple entities with only default values in bulk\n            const bulkInsertMaps: ObjectLiteral[] = []\n            const bulkInsertSubjects: Subject[] = []\n            const singleInsertSubjects: Subject[] = []\n            if (this.queryRunner.connection.driver.options.type === \"mongodb\") {\n                subjects.forEach((subject) => {\n                    if (subject.metadata.createDateColumn && subject.entity) {\n                        subject.entity[\n                            subject.metadata.createDateColumn.databaseName\n                        ] = new Date()\n                    }\n\n                    if (subject.metadata.updateDateColumn && subject.entity) {\n                        subject.entity[\n                            subject.metadata.updateDateColumn.databaseName\n                        ] = new Date()\n                    }\n\n                    subject.createValueSetAndPopChangeMap()\n\n                    bulkInsertSubjects.push(subject)\n                    bulkInsertMaps.push(subject.entity!)\n                })\n            } else if (\n                this.queryRunner.connection.driver.options.type === \"oracle\"\n            ) {\n                subjects.forEach((subject) => {\n                    singleInsertSubjects.push(subject)\n                })\n            } else {\n                subjects.forEach((subject) => {\n                    // we do not insert in bulk in following cases:\n                    // - when there is no values in insert (only defaults are inserted), since we cannot use DEFAULT VALUES expression for multiple inserted rows\n                    // - when entity is a tree table, since tree tables require extra operation per each inserted row\n                    // - when oracle is used, since oracle's bulk insertion is very bad\n                    if (\n                        subject.changeMaps.length === 0 ||\n                        subject.metadata.treeType ||\n                        this.queryRunner.connection.driver.options.type ===\n                            \"oracle\" ||\n                        this.queryRunner.connection.driver.options.type ===\n                            \"sap\"\n                    ) {\n                        singleInsertSubjects.push(subject)\n                    } else {\n                        bulkInsertSubjects.push(subject)\n                        bulkInsertMaps.push(\n                            subject.createValueSetAndPopChangeMap(),\n                        )\n                    }\n                })\n            }\n\n            // for mongodb we have a bit different insertion logic\n            if (\n                InstanceChecker.isMongoEntityManager(this.queryRunner.manager)\n            ) {\n                const insertResult = await this.queryRunner.manager.insert(\n                    subjects[0].metadata.target,\n                    bulkInsertMaps,\n                )\n                subjects.forEach((subject, index) => {\n                    subject.identifier = insertResult.identifiers[index]\n                    subject.generatedMap = insertResult.generatedMaps[index]\n                    subject.insertedValueSet = bulkInsertMaps[index]\n                })\n            } else {\n                // here we execute our insertion query\n                // we need to enable entity updation because we DO need to have updated insertedMap\n                // which is not same object as our entity that's why we don't need to worry about our entity to get dirty\n                // also, we disable listeners because we call them on our own in persistence layer\n                if (bulkInsertMaps.length > 0) {\n                    const insertResult = await this.queryRunner.manager\n                        .createQueryBuilder()\n                        .insert()\n                        .into(subjects[0].metadata.target)\n                        .values(bulkInsertMaps)\n                        .updateEntity(\n                            this.options && this.options.reload === false\n                                ? false\n                                : true,\n                        )\n                        .callListeners(false)\n                        .execute()\n\n                    bulkInsertSubjects.forEach((subject, index) => {\n                        subject.identifier = insertResult.identifiers[index]\n                        subject.generatedMap = insertResult.generatedMaps[index]\n                        subject.insertedValueSet = bulkInsertMaps[index]\n                    })\n                }\n\n                // insert subjects which must be inserted in separate requests (all default values)\n                if (singleInsertSubjects.length > 0) {\n                    for (const subject of singleInsertSubjects) {\n                        subject.insertedValueSet =\n                            subject.createValueSetAndPopChangeMap() // important to have because query builder sets inserted values into it\n\n                        // for nested set we execute additional queries\n                        if (subject.metadata.treeType === \"nested-set\")\n                            await new NestedSetSubjectExecutor(\n                                this.queryRunner,\n                            ).insert(subject)\n\n                        await this.queryRunner.manager\n                            .createQueryBuilder()\n                            .insert()\n                            .into(subject.metadata.target)\n                            .values(subject.insertedValueSet)\n                            .updateEntity(\n                                this.options && this.options.reload === false\n                                    ? false\n                                    : true,\n                            )\n                            .callListeners(false)\n                            .execute()\n                            .then((insertResult) => {\n                                subject.identifier = insertResult.identifiers[0]\n                                subject.generatedMap =\n                                    insertResult.generatedMaps[0]\n                            })\n\n                        // for tree tables we execute additional queries\n                        if (subject.metadata.treeType === \"closure-table\") {\n                            await new ClosureSubjectExecutor(\n                                this.queryRunner,\n                            ).insert(subject)\n                        } else if (\n                            subject.metadata.treeType === \"materialized-path\"\n                        ) {\n                            await new MaterializedPathSubjectExecutor(\n                                this.queryRunner,\n                            ).insert(subject)\n                        }\n                    }\n                }\n            }\n\n            subjects.forEach((subject) => {\n                if (subject.generatedMap) {\n                    subject.metadata.columns.forEach((column) => {\n                        const value = column.getEntityValue(\n                            subject.generatedMap!,\n                        )\n                        if (value !== undefined && value !== null) {\n                            const preparedValue =\n                                this.queryRunner.connection.driver.prepareHydratedValue(\n                                    value,\n                                    column,\n                                )\n                            column.setEntityValue(\n                                subject.generatedMap!,\n                                preparedValue,\n                            )\n                        }\n                    })\n                }\n            })\n        }\n    }\n\n    /**\n     * Updates all given subjects in the database.\n     */\n    protected async executeUpdateOperations(): Promise<void> {\n        const updateSubject = async (subject: Subject) => {\n            if (!subject.identifier)\n                throw new SubjectWithoutIdentifierError(subject)\n\n            // for mongodb we have a bit different updation logic\n            if (\n                InstanceChecker.isMongoEntityManager(this.queryRunner.manager)\n            ) {\n                const partialEntity = this.cloneMongoSubjectEntity(subject)\n                if (\n                    subject.metadata.objectIdColumn &&\n                    subject.metadata.objectIdColumn.propertyName\n                ) {\n                    delete partialEntity[\n                        subject.metadata.objectIdColumn.propertyName\n                    ]\n                }\n\n                if (\n                    subject.metadata.createDateColumn &&\n                    subject.metadata.createDateColumn.propertyName\n                ) {\n                    delete partialEntity[\n                        subject.metadata.createDateColumn.propertyName\n                    ]\n                }\n\n                if (\n                    subject.metadata.updateDateColumn &&\n                    subject.metadata.updateDateColumn.propertyName\n                ) {\n                    partialEntity[\n                        subject.metadata.updateDateColumn.propertyName\n                    ] = new Date()\n                }\n\n                const manager = this.queryRunner.manager as MongoEntityManager\n\n                await manager.update(\n                    subject.metadata.target,\n                    subject.identifier,\n                    partialEntity,\n                )\n            } else {\n                const updateMap: ObjectLiteral =\n                    subject.createValueSetAndPopChangeMap()\n\n                // for tree tables we execute additional queries\n                switch (subject.metadata.treeType) {\n                    case \"nested-set\":\n                        await new NestedSetSubjectExecutor(\n                            this.queryRunner,\n                        ).update(subject)\n                        break\n\n                    case \"closure-table\":\n                        await new ClosureSubjectExecutor(\n                            this.queryRunner,\n                        ).update(subject)\n                        break\n\n                    case \"materialized-path\":\n                        await new MaterializedPathSubjectExecutor(\n                            this.queryRunner,\n                        ).update(subject)\n                        break\n                }\n\n                // here we execute our updation query\n                // we need to enable entity updation because we update a subject identifier\n                // which is not same object as our entity that's why we don't need to worry about our entity to get dirty\n                // also, we disable listeners because we call them on our own in persistence layer\n                const updateQueryBuilder = this.queryRunner.manager\n                    .createQueryBuilder()\n                    .update(subject.metadata.target)\n                    .set(updateMap)\n                    .updateEntity(\n                        this.options && this.options.reload === false\n                            ? false\n                            : true,\n                    )\n                    .callListeners(false)\n\n                if (subject.entity) {\n                    updateQueryBuilder.whereEntity(subject.identifier)\n                } else {\n                    // in this case identifier is just conditions object to update by\n                    updateQueryBuilder.where(subject.identifier)\n                }\n\n                const updateResult = await updateQueryBuilder.execute()\n                const updateGeneratedMap = updateResult.generatedMaps[0]\n                if (updateGeneratedMap) {\n                    subject.metadata.columns.forEach((column) => {\n                        const value = column.getEntityValue(updateGeneratedMap!)\n                        if (value !== undefined && value !== null) {\n                            const preparedValue =\n                                this.queryRunner.connection.driver.prepareHydratedValue(\n                                    value,\n                                    column,\n                                )\n                            column.setEntityValue(\n                                updateGeneratedMap!,\n                                preparedValue,\n                            )\n                        }\n                    })\n                    if (!subject.generatedMap) {\n                        subject.generatedMap = {}\n                    }\n                    Object.assign(subject.generatedMap, updateGeneratedMap)\n                }\n            }\n        }\n\n        // Nested sets need to be updated one by one\n        // Split array in two, one with nested set subjects and the other with the remaining subjects\n        const nestedSetSubjects: Subject[] = []\n        const remainingSubjects: Subject[] = []\n\n        for (const subject of this.updateSubjects) {\n            if (subject.metadata.treeType === \"nested-set\") {\n                nestedSetSubjects.push(subject)\n            } else {\n                remainingSubjects.push(subject)\n            }\n        }\n\n        // Run nested set updates one by one\n        const nestedSetPromise = new Promise<void>(async (ok, fail) => {\n            for (const subject of nestedSetSubjects) {\n                try {\n                    await updateSubject(subject)\n                } catch (error) {\n                    fail(error)\n                }\n            }\n            ok()\n        })\n\n        // Run all remaining subjects in parallel\n        await Promise.all([\n            ...remainingSubjects.map(updateSubject),\n            nestedSetPromise,\n        ])\n    }\n\n    /**\n     * Removes all given subjects from the database.\n     *\n     * todo: we need to apply topological sort here as well\n     */\n    protected async executeRemoveOperations(): Promise<void> {\n        // group insertion subjects to make bulk insertions\n        const [groupedRemoveSubjects, groupedRemoveSubjectKeys] =\n            this.groupBulkSubjects(this.removeSubjects, \"delete\")\n\n        for (const groupName of groupedRemoveSubjectKeys) {\n            const subjects = groupedRemoveSubjects[groupName]\n            const deleteMaps = subjects.map((subject) => {\n                if (!subject.identifier)\n                    throw new SubjectWithoutIdentifierError(subject)\n\n                return subject.identifier\n            })\n\n            // for mongodb we have a bit different updation logic\n            if (\n                InstanceChecker.isMongoEntityManager(this.queryRunner.manager)\n            ) {\n                const manager = this.queryRunner.manager as MongoEntityManager\n                await manager.delete(subjects[0].metadata.target, deleteMaps)\n            } else {\n                // for tree tables we execute additional queries\n                switch (subjects[0].metadata.treeType) {\n                    case \"nested-set\":\n                        await new NestedSetSubjectExecutor(\n                            this.queryRunner,\n                        ).remove(subjects)\n                        break\n\n                    case \"closure-table\":\n                        await new ClosureSubjectExecutor(\n                            this.queryRunner,\n                        ).remove(subjects)\n                        break\n                }\n\n                // here we execute our deletion query\n                // we don't need to specify entities and set update entity to true since the only thing query builder\n                // will do for use is a primary keys deletion which is handled by us later once persistence is finished\n                // also, we disable listeners because we call them on our own in persistence layer\n                await this.queryRunner.manager\n                    .createQueryBuilder()\n                    .delete()\n                    .from(subjects[0].metadata.target)\n                    .where(deleteMaps)\n                    .callListeners(false)\n                    .execute()\n            }\n        }\n    }\n\n    private cloneMongoSubjectEntity(subject: Subject): ObjectLiteral {\n        const target: ObjectLiteral = {}\n\n        if (subject.entity) {\n            for (const column of subject.metadata.columns) {\n                OrmUtils.mergeDeep(\n                    target,\n                    column.getEntityValueMap(subject.entity),\n                )\n            }\n        }\n\n        return target\n    }\n\n    /**\n     * Soft-removes all given subjects in the database.\n     */\n    protected async executeSoftRemoveOperations(): Promise<void> {\n        await Promise.all(\n            this.softRemoveSubjects.map(async (subject) => {\n                if (!subject.identifier)\n                    throw new SubjectWithoutIdentifierError(subject)\n\n                let updateResult: UpdateResult\n\n                // for mongodb we have a bit different updation logic\n                if (\n                    InstanceChecker.isMongoEntityManager(\n                        this.queryRunner.manager,\n                    )\n                ) {\n                    const partialEntity = this.cloneMongoSubjectEntity(subject)\n                    if (\n                        subject.metadata.objectIdColumn &&\n                        subject.metadata.objectIdColumn.propertyName\n                    ) {\n                        delete partialEntity[\n                            subject.metadata.objectIdColumn.propertyName\n                        ]\n                    }\n\n                    if (\n                        subject.metadata.createDateColumn &&\n                        subject.metadata.createDateColumn.propertyName\n                    ) {\n                        delete partialEntity[\n                            subject.metadata.createDateColumn.propertyName\n                        ]\n                    }\n\n                    if (\n                        subject.metadata.updateDateColumn &&\n                        subject.metadata.updateDateColumn.propertyName\n                    ) {\n                        partialEntity[\n                            subject.metadata.updateDateColumn.propertyName\n                        ] = new Date()\n                    }\n\n                    if (\n                        subject.metadata.deleteDateColumn &&\n                        subject.metadata.deleteDateColumn.propertyName\n                    ) {\n                        partialEntity[\n                            subject.metadata.deleteDateColumn.propertyName\n                        ] = new Date()\n                    }\n\n                    const manager = this.queryRunner\n                        .manager as MongoEntityManager\n\n                    updateResult = await manager.update(\n                        subject.metadata.target,\n                        subject.identifier,\n                        partialEntity,\n                    )\n                } else {\n                    // here we execute our soft-deletion query\n                    // we need to enable entity soft-deletion because we update a subject identifier\n                    // which is not same object as our entity that's why we don't need to worry about our entity to get dirty\n                    // also, we disable listeners because we call them on our own in persistence layer\n                    const softDeleteQueryBuilder = this.queryRunner.manager\n                        .createQueryBuilder()\n                        .softDelete()\n                        .from(subject.metadata.target)\n                        .updateEntity(\n                            this.options && this.options.reload === false\n                                ? false\n                                : true,\n                        )\n                        .callListeners(false)\n\n                    if (subject.entity) {\n                        softDeleteQueryBuilder.whereEntity(subject.identifier)\n                    } else {\n                        // in this case identifier is just conditions object to update by\n                        softDeleteQueryBuilder.where(subject.identifier)\n                    }\n\n                    updateResult = await softDeleteQueryBuilder.execute()\n                }\n\n                subject.generatedMap = updateResult.generatedMaps[0]\n                if (subject.generatedMap) {\n                    subject.metadata.columns.forEach((column) => {\n                        const value = column.getEntityValue(\n                            subject.generatedMap!,\n                        )\n                        if (value !== undefined && value !== null) {\n                            const preparedValue =\n                                this.queryRunner.connection.driver.prepareHydratedValue(\n                                    value,\n                                    column,\n                                )\n                            column.setEntityValue(\n                                subject.generatedMap!,\n                                preparedValue,\n                            )\n                        }\n                    })\n                }\n\n                // experiments, remove probably, need to implement tree tables children removal\n                // if (subject.updatedRelationMaps.length > 0) {\n                //     await Promise.all(subject.updatedRelationMaps.map(async updatedRelation => {\n                //         if (!updatedRelation.relation.isTreeParent) return;\n                //         if (!updatedRelation.value !== null) return;\n                //\n                //         if (subject.metadata.treeType === \"closure-table\") {\n                //             await new ClosureSubjectExecutor(this.queryRunner).deleteChildrenOf(subject);\n                //         }\n                //     }));\n                // }\n            }),\n        )\n    }\n\n    /**\n     * Recovers all given subjects in the database.\n     */\n    protected async executeRecoverOperations(): Promise<void> {\n        await Promise.all(\n            this.recoverSubjects.map(async (subject) => {\n                if (!subject.identifier)\n                    throw new SubjectWithoutIdentifierError(subject)\n\n                let updateResult: UpdateResult\n\n                // for mongodb we have a bit different updation logic\n                if (\n                    InstanceChecker.isMongoEntityManager(\n                        this.queryRunner.manager,\n                    )\n                ) {\n                    const partialEntity = this.cloneMongoSubjectEntity(subject)\n                    if (\n                        subject.metadata.objectIdColumn &&\n                        subject.metadata.objectIdColumn.propertyName\n                    ) {\n                        delete partialEntity[\n                            subject.metadata.objectIdColumn.propertyName\n                        ]\n                    }\n\n                    if (\n                        subject.metadata.createDateColumn &&\n                        subject.metadata.createDateColumn.propertyName\n                    ) {\n                        delete partialEntity[\n                            subject.metadata.createDateColumn.propertyName\n                        ]\n                    }\n\n                    if (\n                        subject.metadata.updateDateColumn &&\n                        subject.metadata.updateDateColumn.propertyName\n                    ) {\n                        partialEntity[\n                            subject.metadata.updateDateColumn.propertyName\n                        ] = new Date()\n                    }\n\n                    if (\n                        subject.metadata.deleteDateColumn &&\n                        subject.metadata.deleteDateColumn.propertyName\n                    ) {\n                        partialEntity[\n                            subject.metadata.deleteDateColumn.propertyName\n                        ] = null\n                    }\n\n                    const manager = this.queryRunner\n                        .manager as MongoEntityManager\n\n                    updateResult = await manager.update(\n                        subject.metadata.target,\n                        subject.identifier,\n                        partialEntity,\n                    )\n                } else {\n                    // here we execute our restory query\n                    // we need to enable entity restory because we update a subject identifier\n                    // which is not same object as our entity that's why we don't need to worry about our entity to get dirty\n                    // also, we disable listeners because we call them on our own in persistence layer\n                    const softDeleteQueryBuilder = this.queryRunner.manager\n                        .createQueryBuilder()\n                        .restore()\n                        .from(subject.metadata.target)\n                        .updateEntity(\n                            this.options && this.options.reload === false\n                                ? false\n                                : true,\n                        )\n                        .callListeners(false)\n\n                    if (subject.entity) {\n                        softDeleteQueryBuilder.whereEntity(subject.identifier)\n                    } else {\n                        // in this case identifier is just conditions object to update by\n                        softDeleteQueryBuilder.where(subject.identifier)\n                    }\n\n                    updateResult = await softDeleteQueryBuilder.execute()\n                }\n\n                subject.generatedMap = updateResult.generatedMaps[0]\n                if (subject.generatedMap) {\n                    subject.metadata.columns.forEach((column) => {\n                        const value = column.getEntityValue(\n                            subject.generatedMap!,\n                        )\n                        if (value !== undefined && value !== null) {\n                            const preparedValue =\n                                this.queryRunner.connection.driver.prepareHydratedValue(\n                                    value,\n                                    column,\n                                )\n                            column.setEntityValue(\n                                subject.generatedMap!,\n                                preparedValue,\n                            )\n                        }\n                    })\n                }\n\n                // experiments, remove probably, need to implement tree tables children removal\n                // if (subject.updatedRelationMaps.length > 0) {\n                //     await Promise.all(subject.updatedRelationMaps.map(async updatedRelation => {\n                //         if (!updatedRelation.relation.isTreeParent) return;\n                //         if (!updatedRelation.value !== null) return;\n                //\n                //         if (subject.metadata.treeType === \"closure-table\") {\n                //             await new ClosureSubjectExecutor(this.queryRunner).deleteChildrenOf(subject);\n                //         }\n                //     }));\n                // }\n            }),\n        )\n    }\n\n    /**\n     * Updates all special columns of the saving entities (create date, update date, version, etc.).\n     * Also updates nullable columns and columns with default values.\n     */\n    protected updateSpecialColumnsInPersistedEntities(): void {\n        // update inserted entity properties\n        if (this.insertSubjects.length)\n            this.updateSpecialColumnsInInsertedAndUpdatedEntities(\n                this.insertSubjects,\n            )\n\n        // update updated entity properties\n        if (this.updateSubjects.length)\n            this.updateSpecialColumnsInInsertedAndUpdatedEntities(\n                this.updateSubjects,\n            )\n\n        // update soft-removed entity properties\n        if (this.softRemoveSubjects.length)\n            this.updateSpecialColumnsInInsertedAndUpdatedEntities(\n                this.softRemoveSubjects,\n            )\n\n        // update recovered entity properties\n        if (this.recoverSubjects.length)\n            this.updateSpecialColumnsInInsertedAndUpdatedEntities(\n                this.recoverSubjects,\n            )\n\n        // remove ids from the entities that were removed\n        if (this.removeSubjects.length) {\n            this.removeSubjects.forEach((subject) => {\n                if (!subject.entity) return\n\n                subject.metadata.primaryColumns.forEach((primaryColumn) => {\n                    primaryColumn.setEntityValue(subject.entity!, undefined)\n                })\n            })\n        }\n\n        // other post-persist updations\n        this.allSubjects.forEach((subject) => {\n            if (!subject.entity) return\n\n            subject.metadata.relationIds.forEach((relationId) => {\n                relationId.setValue(subject.entity!)\n            })\n\n            // mongo _id remove\n            if (\n                InstanceChecker.isMongoEntityManager(this.queryRunner.manager)\n            ) {\n                if (\n                    subject.metadata.objectIdColumn &&\n                    subject.metadata.objectIdColumn.databaseName &&\n                    subject.metadata.objectIdColumn.databaseName !==\n                        subject.metadata.objectIdColumn.propertyName\n                ) {\n                    delete subject.entity[\n                        subject.metadata.objectIdColumn.databaseName\n                    ]\n                }\n            }\n        })\n    }\n\n    /**\n     * Updates all special columns of the saving entities (create date, update date, version, etc.).\n     * Also updates nullable columns and columns with default values.\n     */\n    protected updateSpecialColumnsInInsertedAndUpdatedEntities(\n        subjects: Subject[],\n    ): void {\n        subjects.forEach((subject) => {\n            if (!subject.entity) return\n\n            // set values to \"null\" for nullable columns that did not have values\n            subject.metadata.columns.forEach((column) => {\n                // if table inheritance is used make sure this column is not child's column\n                if (\n                    subject.metadata.childEntityMetadatas.length > 0 &&\n                    subject.metadata.childEntityMetadatas\n                        .map((metadata) => metadata.target)\n                        .indexOf(column.target) !== -1\n                )\n                    return\n\n                // entities does not have virtual columns\n                if (column.isVirtual) return\n\n                // if column is deletedAt\n                if (column.isDeleteDate) return\n\n                // update nullable columns\n                if (column.isNullable) {\n                    const columnValue = column.getEntityValue(subject.entity!)\n                    if (columnValue === undefined)\n                        column.setEntityValue(subject.entity!, null)\n                }\n\n                // update relational columns\n                if (subject.updatedRelationMaps.length > 0) {\n                    subject.updatedRelationMaps.forEach(\n                        (updatedRelationMap) => {\n                            updatedRelationMap.relation.joinColumns.forEach(\n                                (column) => {\n                                    if (column.isVirtual === true) return\n\n                                    column.setEntityValue(\n                                        subject.entity!,\n                                        ObjectUtils.isObject(\n                                            updatedRelationMap.value,\n                                        )\n                                            ? column.referencedColumn!.getEntityValue(\n                                                  updatedRelationMap.value,\n                                              )\n                                            : updatedRelationMap.value,\n                                    )\n                                },\n                            )\n                        },\n                    )\n                }\n            })\n\n            // merge into entity all generated values returned by a database\n            if (subject.generatedMap)\n                this.queryRunner.manager.merge(\n                    subject.metadata.target as any,\n                    subject.entity,\n                    subject.generatedMap,\n                )\n        })\n    }\n\n    /**\n     * Groups subjects by metadata names (by tables) to make bulk insertions and deletions possible.\n     * However there are some limitations with bulk insertions of data into tables with generated (increment) columns\n     * in some drivers. Some drivers like mysql and sqlite does not support returning multiple generated columns\n     * after insertion and can only return a single generated column value, that's why its not possible to do bulk insertion,\n     * because it breaks insertion result's generatedMap and leads to problems when this subject is used in other subjects saves.\n     * That's why we only support bulking in junction tables for those drivers.\n     *\n     * Other drivers like postgres and sql server support RETURNING / OUTPUT statement which allows to return generated\n     * id for each inserted row, that's why bulk insertion is not limited to junction tables in there.\n     */\n    protected groupBulkSubjects(\n        subjects: Subject[],\n        type: \"insert\" | \"delete\",\n    ): [{ [key: string]: Subject[] }, string[]] {\n        const group: { [key: string]: Subject[] } = {}\n        const keys: string[] = []\n        const hasReturningDependColumns = subjects.some((subject) => {\n            return subject.metadata.getInsertionReturningColumns().length > 0\n        })\n        const groupingAllowed =\n            type === \"delete\" ||\n            this.queryRunner.connection.driver.isReturningSqlSupported(\n                \"insert\",\n            ) ||\n            hasReturningDependColumns === false\n\n        subjects.forEach((subject, index) => {\n            const key =\n                groupingAllowed || subject.metadata.isJunction\n                    ? subject.metadata.name\n                    : subject.metadata.name + \"_\" + index\n            if (!group[key]) {\n                group[key] = [subject]\n                keys.push(key)\n            } else {\n                group[key].push(subject)\n            }\n        })\n\n        return [group, keys]\n    }\n}\n"], "sourceRoot": ".."}