"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigController = void 0;
const common_1 = require("@nestjs/common");
const config_service_1 = require("./config.service");
let ConfigController = class ConfigController {
    configService;
    constructor(configService) {
        this.configService = configService;
    }
    async getAllConfigs() {
        return this.configService.getAllConfigs();
    }
    async getWinModifier() {
        const modifier = await this.configService.getWinModifier();
        return { win_modifier: modifier };
    }
    async setWinModifier(body) {
        if (body.modifier === undefined) {
            throw new common_1.BadRequestException('Missing modifier value');
        }
        if (body.modifier < -0.3 || body.modifier > 0.3) {
            throw new common_1.BadRequestException('Modifier must be between -0.3 and 0.3');
        }
        await this.configService.setWinModifier(body.modifier);
        return { success: true, modifier: body.modifier };
    }
    async setConfig(body) {
        if (!body.key || body.value === undefined) {
            throw new common_1.BadRequestException('Missing key or value');
        }
        const config = await this.configService.setConfig(body.key, body.value);
        return config;
    }
};
exports.ConfigController = ConfigController;
__decorate([
    (0, common_1.Get)('all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ConfigController.prototype, "getAllConfigs", null);
__decorate([
    (0, common_1.Get)('win-modifier'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ConfigController.prototype, "getWinModifier", null);
__decorate([
    (0, common_1.Post)('win-modifier'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ConfigController.prototype, "setWinModifier", null);
__decorate([
    (0, common_1.Post)('set'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ConfigController.prototype, "setConfig", null);
exports.ConfigController = ConfigController = __decorate([
    (0, common_1.Controller)('config'),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], ConfigController);
//# sourceMappingURL=config.controller.js.map