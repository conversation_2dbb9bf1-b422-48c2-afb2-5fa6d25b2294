"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("./user.entity");
let UserService = class UserService {
    userRepository;
    constructor(userRepository) {
        this.userRepository = userRepository;
    }
    async createUser(telegram_id, username, firstname) {
        let user = await this.userRepository.findOne({ where: { telegram_id } });
        if (!user) {
            user = this.userRepository.create({
                id: telegram_id,
                telegram_id,
                username,
                firstname,
                balance_ton: 0,
            });
            await this.userRepository.save(user);
        }
        return user;
    }
    async getUser(telegram_id) {
        return this.userRepository.findOne({ where: { telegram_id } });
    }
    async updateBalance(telegram_id, amount) {
        const user = await this.getUser(telegram_id);
        if (user) {
            user.balance_ton = Number(user.balance_ton) + Number(amount);
            await this.userRepository.save(user);
            return user;
        }
        return null;
    }
    async deductBalance(telegram_id, amount) {
        const user = await this.getUser(telegram_id);
        if (user && Number(user.balance_ton) >= Number(amount)) {
            user.balance_ton = Number(user.balance_ton) - Number(amount);
            await this.userRepository.save(user);
            return true;
        }
        return false;
    }
    async getLeaderboard() {
        return this.userRepository.find({
            order: { balance_ton: 'DESC' },
            take: 10,
            select: ['telegram_id', 'username', 'firstname', 'balance_ton'],
        });
    }
    async getUserById(telegram_id) {
        return this.userRepository.findOne({ where: { telegram_id } });
    }
    async banUser(telegram_id) {
        const user = await this.getUser(telegram_id);
        if (user) {
            user.is_banned = true;
            await this.userRepository.save(user);
            return true;
        }
        return false;
    }
    async unbanUser(telegram_id) {
        const user = await this.getUser(telegram_id);
        if (user) {
            user.is_banned = false;
            await this.userRepository.save(user);
            return true;
        }
        return false;
    }
    async updateWalletAddress(telegram_id, wallet_address) {
        const user = await this.getUser(telegram_id);
        if (user) {
            user.ton_wallet_address = wallet_address;
            await this.userRepository.save(user);
            return user;
        }
        return null;
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UserService);
//# sourceMappingURL=user.service.js.map