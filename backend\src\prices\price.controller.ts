import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  NotFoundException,
  BadRequestException,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { PriceService } from './price.service';

@Controller('prices')
export class PriceController {
  constructor(private readonly priceService: PriceService) {}

  @Get()
  async getAllPrices() {
    return this.priceService.getAllPrices();
  }

  @Get(':model_key')
  async getPrice(@Param('model_key') model_key: string) {
    const price = await this.priceService.getPriceByModelKey(model_key);
    if (!price) {
      throw new NotFoundException('Price not found');
    }
    return price;
  }

  @Post('update')
  async updatePrice(@Body() body: { model_key: string; min_ton: number; image_url: string }) {
    if (!body.model_key || body.min_ton === undefined || !body.image_url) {
      throw new BadRequestException('Missing required fields');
    }
    return this.priceService.updatePrice(body.model_key, body.min_ton, body.image_url);
  }

  @Post('import')
  @UseInterceptors(FileInterceptor('file'))
  async importPrices(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    if (!file.originalname.match(/\.(xlsx|xls)$/)) {
      throw new BadRequestException('Only Excel files are allowed');
    }

    const result = await this.priceService.importFromExcel(file.buffer);
    return result;
  }

  @Post('clear')
  async clearAllPrices() {
    await this.priceService.clearAllPrices();
    return { success: true };
  }
}
