"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoinflipService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const coinflip_room_entity_1 = require("./coinflip-room.entity");
const user_service_1 = require("../users/user.service");
const transaction_service_1 = require("../transactions/transaction.service");
const transaction_entity_1 = require("../transactions/transaction.entity");
const crypto = require("crypto");
let CoinflipService = class CoinflipService {
    coinflipRepository;
    userService;
    transactionService;
    constructor(coinflipRepository, userService, transactionService) {
        this.coinflipRepository = coinflipRepository;
        this.userService = userService;
        this.transactionService = transactionService;
    }
    async createRoom(playerA_id, stake) {
        const user = await this.userService.getUser(playerA_id);
        if (!user || user.is_banned || Number(user.balance_ton) < stake) {
            return null;
        }
        const success = await this.userService.deductBalance(playerA_id, stake);
        if (!success) {
            return null;
        }
        await this.transactionService.createTransaction(playerA_id, transaction_entity_1.TransactionKind.GAME_BET, stake, undefined, 'coinflip', { role: 'creator' });
        const room = this.coinflipRepository.create({
            stake,
            playerA_id,
            status: coinflip_room_entity_1.CoinflipStatus.WAITING,
            seed: crypto.randomBytes(32).toString('hex'),
        });
        return this.coinflipRepository.save(room);
    }
    async joinRoom(room_id, playerB_id) {
        const room = await this.coinflipRepository.findOne({ where: { id: room_id } });
        if (!room || room.status !== coinflip_room_entity_1.CoinflipStatus.WAITING || room.playerA_id === playerB_id) {
            return { success: false };
        }
        const user = await this.userService.getUser(playerB_id);
        if (!user || user.is_banned || Number(user.balance_ton) < room.stake) {
            return { success: false };
        }
        const success = await this.userService.deductBalance(playerB_id, room.stake);
        if (!success) {
            return { success: false };
        }
        await this.transactionService.createTransaction(playerB_id, transaction_entity_1.TransactionKind.GAME_BET, room.stake, undefined, 'coinflip', { role: 'joiner', room_id });
        room.playerB_id = playerB_id;
        room.status = coinflip_room_entity_1.CoinflipStatus.PLAYING;
        const result = await this.playGame(room);
        await this.coinflipRepository.save(room);
        return {
            success: true,
            room,
            winner: result.winner,
            result: result.coinResult,
        };
    }
    async playGame(room) {
        const hash = crypto.createHash('sha256').update(room.seed + room.id.toString()).digest('hex');
        const random = parseInt(hash.substring(0, 8), 16) / 0xffffffff;
        const coinResult = random < 0.5 ? 'heads' : 'tails';
        const winner = random < 0.5 ? room.playerA_id : room.playerB_id;
        const totalPot = room.stake * 2;
        const rake = totalPot * 0.1;
        const payout = totalPot - rake;
        room.result = coinResult;
        room.winner_id = winner;
        room.status = coinflip_room_entity_1.CoinflipStatus.FINISHED;
        await this.userService.updateBalance(winner, payout);
        await this.transactionService.createTransaction(winner, transaction_entity_1.TransactionKind.GAME_WIN, payout, undefined, 'coinflip', {
            room_id: room.id,
            result: coinResult,
            opponent: winner === room.playerA_id ? room.playerB_id : room.playerA_id,
        });
        return { winner, coinResult };
    }
    async getWaitingRooms() {
        return this.coinflipRepository.find({
            where: { status: coinflip_room_entity_1.CoinflipStatus.WAITING },
            order: { created_at: 'DESC' },
            take: 20,
        });
    }
    async getRecentGames() {
        return this.coinflipRepository.find({
            where: { status: coinflip_room_entity_1.CoinflipStatus.FINISHED },
            order: { updated_at: 'DESC' },
            take: 10,
        });
    }
    async getUserRooms(user_id) {
        return this.coinflipRepository
            .createQueryBuilder('room')
            .where('room.playerA_id = :user_id OR room.playerB_id = :user_id', { user_id })
            .orderBy('room.created_at', 'DESC')
            .take(20)
            .getMany();
    }
    async getRoomById(room_id) {
        return this.coinflipRepository.findOne({ where: { id: room_id } });
    }
};
exports.CoinflipService = CoinflipService;
exports.CoinflipService = CoinflipService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(coinflip_room_entity_1.CoinflipRoom)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        user_service_1.UserService,
        transaction_service_1.TransactionService])
], CoinflipService);
//# sourceMappingURL=coinflip.service.js.map