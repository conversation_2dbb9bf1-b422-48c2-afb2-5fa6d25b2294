import { Repository } from 'typeorm';
import { CoinflipRoom } from './coinflip-room.entity';
import { UserService } from '../users/user.service';
import { TransactionService } from '../transactions/transaction.service';
export declare class CoinflipService {
    private coinflipRepository;
    private userService;
    private transactionService;
    constructor(coinflipRepository: Repository<CoinflipRoom>, userService: UserService, transactionService: TransactionService);
    createRoom(playerA_id: string, stake: number): Promise<CoinflipRoom | null>;
    joinRoom(room_id: number, playerB_id: string): Promise<{
        success: boolean;
        room?: CoinflipRoom;
        winner?: string;
        result?: string;
    }>;
    private playGame;
    getWaitingRooms(): Promise<CoinflipRoom[]>;
    getRecentGames(): Promise<CoinflipRoom[]>;
    getUserRooms(user_id: string): Promise<CoinflipRoom[]>;
    getRoomById(room_id: number): Promise<CoinflipRoom | null>;
}
