import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Di<PERSON>,
  Play,
  Coins,
  Loader2
} from 'lucide-react';
import ApiService from '../../services/ApiService';
import './Games.css';

const DoubleGame = ({ user, balance, updateBalance }) => {
  const [betAmount, setBetAmount] = useState('');
  const [selectedMultiplier, setSelectedMultiplier] = useState(null);
  const [loading, setLoading] = useState(false);
  const [gameResult, setGameResult] = useState(null);

  const multipliers = [
    { value: 2, probability: 45, color: '#00ff88' },
    { value: 3, probability: 30, color: '#ffd700' },
    { value: 5, probability: 20, color: '#ff6b6b' },
    { value: 20, probability: 5, color: '#ff4757' },
  ];

  const playGame = async () => {
    if (!betAmount || parseFloat(betAmount) <= 0) {
      alert('Введите корректную сумму ставки');
      return;
    }

    if (parseFloat(betAmount) > balance) {
      alert('Недостаточно средств');
      return;
    }

    if (!selectedMultiplier) {
      alert('Выберите множитель');
      return;
    }

    setLoading(true);
    setGameResult(null);

    try {
      const result = await ApiService.playDouble(
        user.telegram_id,
        parseFloat(betAmount),
        selectedMultiplier
      );

      if (result.success) {
        const newBalance = balance - parseFloat(betAmount) + (result.payout || 0);
        updateBalance(newBalance);
        
        setGameResult({
          won: result.payout > 0,
          resultMultiplier: result.result_multiplier,
          chosenMultiplier: selectedMultiplier,
          payout: result.payout || 0,
          betAmount: parseFloat(betAmount),
        });
      }
    } catch (error) {
      console.error('Error playing double:', error);
      alert('Ошибка при игре');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="double-game">
      <motion.div
        className="game-header card-elevated"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-title-1 flex items-center gap-3">
          <Dices size={28} className="text-danger" />
          Double Roulette
        </h2>
        <p className="text-callout text-secondary-label">Выберите множитель и испытайте удачу</p>
      </motion.div>

      <motion.div
        className="multipliers-grid card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <h3 className="text-title-2 text-center">Выберите множитель</h3>
        <div className="multipliers">
          {multipliers.map((mult) => (
            <motion.button
              key={mult.value}
              className={`multiplier-btn ${selectedMultiplier === mult.value ? 'selected' : ''}`}
              style={{ 
                borderColor: mult.color,
                backgroundColor: selectedMultiplier === mult.value ? `${mult.color}20` : 'transparent'
              }}
              onClick={() => setSelectedMultiplier(mult.value)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={loading}
            >
              <div className="multiplier-value" style={{ color: mult.color }}>
                {mult.value}x
              </div>
              <div className="multiplier-chance">
                {mult.probability}%
              </div>
            </motion.button>
          ))}
        </div>
      </motion.div>

      <motion.div
        className="bet-controls card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h3 className="text-title-2 text-center">Сумма ставки</h3>
        <div className="bet-input relative">
          <input
            type="number"
            value={betAmount}
            onChange={(e) => setBetAmount(e.target.value)}
            placeholder="0.00"
            className="input"
            disabled={loading}
            step="0.01"
            min="0.01"
          />
          <Coins size={20} className="input-icon text-success absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none" />
        </div>

        <div className="quick-bets">
          {['0.1', '0.5', '1.0', '2.0'].map(amount => (
            <button
              key={amount}
              className="quick-bet-btn btn btn-secondary btn-sm"
              onClick={() => setBetAmount(amount)}
              disabled={loading}
            >
              {amount}
            </button>
          ))}
        </div>

        <button
          className="btn btn-primary btn-lg w-full"
          onClick={playGame}
          disabled={loading || !betAmount || !selectedMultiplier}
        >
          {loading ? (
            <Loader2 size={20} className="animate-pulse" />
          ) : (
            <Play size={20} />
          )}
          <span>{loading ? 'Игра...' : 'Играть'}</span>
        </button>
      </motion.div>

      {gameResult && (
        <motion.div
          className={`game-result card ${gameResult.won ? 'win' : 'lose'}`}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="result-icon text-4xl">
            {gameResult.won ? '🎉' : '😔'}
          </div>
          <div className="result-text text-success">
            {gameResult.won ? 'Победа!' : 'Проигрыш!'}
          </div>
          <div className="result-details">
            <p className="text-callout">Выпало: <span className="font-bold">{gameResult.resultMultiplier}x</span></p>
            <p className="text-callout">Ваш выбор: <span className="font-bold">{gameResult.chosenMultiplier}x</span></p>
            {gameResult.won && (
              <p className="result-amount text-success">
                Выигрыш: +{gameResult.payout.toFixed(2)} TON
              </p>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DoubleGame;
