"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoinflipRoom = exports.CoinflipStatus = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../users/user.entity");
var CoinflipStatus;
(function (CoinflipStatus) {
    CoinflipStatus["WAITING"] = "WAITING";
    CoinflipStatus["PLAYING"] = "PLAYING";
    CoinflipStatus["FINISHED"] = "FINISHED";
})(CoinflipStatus || (exports.CoinflipStatus = CoinflipStatus = {}));
let CoinflipRoom = class CoinflipRoom {
    id;
    stake;
    playerA;
    playerA_id;
    playerB;
    playerB_id;
    winner;
    winner_id;
    status;
    result;
    seed;
    created_at;
    updated_at;
};
exports.CoinflipRoom = CoinflipRoom;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], CoinflipRoom.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], CoinflipRoom.prototype, "stake", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    __metadata("design:type", user_entity_1.User)
], CoinflipRoom.prototype, "playerA", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CoinflipRoom.prototype, "playerA_id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    __metadata("design:type", user_entity_1.User)
], CoinflipRoom.prototype, "playerB", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CoinflipRoom.prototype, "playerB_id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    __metadata("design:type", user_entity_1.User)
], CoinflipRoom.prototype, "winner", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CoinflipRoom.prototype, "winner_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: CoinflipStatus.WAITING }),
    __metadata("design:type", String)
], CoinflipRoom.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CoinflipRoom.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CoinflipRoom.prototype, "seed", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CoinflipRoom.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CoinflipRoom.prototype, "updated_at", void 0);
exports.CoinflipRoom = CoinflipRoom = __decorate([
    (0, typeorm_1.Entity)()
], CoinflipRoom);
//# sourceMappingURL=coinflip-room.entity.js.map