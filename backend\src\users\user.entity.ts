import { <PERSON><PERSON>ty, Column, PrimaryColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity()
export class User {
  @PrimaryColumn()
  id: string;

  @Column()
  telegram_id: string;

  @Column()
  username: string;

  @Column()
  firstname: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  balance_ton: number;

  @Column({ nullable: true })
  ton_wallet_address: string;

  @Column({ default: false })
  is_banned: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
