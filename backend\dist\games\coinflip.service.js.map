{"version": 3, "file": "coinflip.service.js", "sourceRoot": "", "sources": ["../../src/games/coinflip.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,iEAAsE;AACtE,wDAAoD;AACpD,6EAAyE;AACzE,2EAAqE;AACrE,iCAAiC;AAG1B,IAAM,eAAe,GAArB,MAAM,eAAe;IAGhB;IACA;IACA;IAJV,YAEU,kBAA4C,EAC5C,WAAwB,EACxB,kBAAsC;QAFtC,uBAAkB,GAAlB,kBAAkB,CAA0B;QAC5C,gBAAW,GAAX,WAAW,CAAa;QACxB,uBAAkB,GAAlB,kBAAkB,CAAoB;IAC7C,CAAC;IAEJ,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,KAAa;QAChD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACxE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAC7C,UAAU,EACV,oCAAe,CAAC,QAAQ,EACxB,KAAK,EACL,SAAS,EACT,UAAU,EACV,EAAE,IAAI,EAAE,SAAS,EAAE,CACpB,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC1C,KAAK;YACL,UAAU;YACV,MAAM,EAAE,qCAAc,CAAC,OAAO;YAC9B,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;SAC7C,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAe,EAAE,UAAkB;QAMhD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;QAC/E,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,qCAAc,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;YACtF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;QAC5B,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YACrE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;QAC5B,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7E,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;QAC5B,CAAC;QAGD,MAAM,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAC7C,UAAU,EACV,oCAAe,CAAC,QAAQ,EACxB,IAAI,CAAC,KAAK,EACV,SAAS,EACT,UAAU,EACV,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAC5B,CAAC;QAGF,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,qCAAc,CAAC,OAAO,CAAC;QAGrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEzC,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzC,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,MAAM,EAAE,MAAM,CAAC,UAAU;SAC1B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAkB;QAEvC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9F,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC;QAE/D,MAAM,UAAU,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;QACpD,MAAM,MAAM,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;QAGhE,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAChC,MAAM,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC;QAC5B,MAAM,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC;QAG/B,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,qCAAc,CAAC,QAAQ,CAAC;QAGtC,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACrD,MAAM,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAC7C,MAAM,EACN,oCAAe,CAAC,QAAQ,EACxB,MAAM,EACN,SAAS,EACT,UAAU,EACV;YACE,OAAO,EAAE,IAAI,CAAC,EAAE;YAChB,MAAM,EAAE,UAAU;YAClB,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU;SACzE,CACF,CAAC;QAEF,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE,EAAE,MAAM,EAAE,qCAAc,CAAC,OAAO,EAAE;YACzC,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YAC7B,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE,EAAE,MAAM,EAAE,qCAAc,CAAC,QAAQ,EAAE;YAC1C,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;YAC7B,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAAe;QAChC,OAAO,IAAI,CAAC,kBAAkB;aAC3B,kBAAkB,CAAC,MAAM,CAAC;aAC1B,KAAK,CAAC,0DAA0D,EAAE,EAAE,OAAO,EAAE,CAAC;aAC9E,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;aAClC,IAAI,CAAC,EAAE,CAAC;aACR,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAe;QAC/B,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;IACrE,CAAC;CACF,CAAA;AAzJY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;qCACH,oBAAU;QACjB,0BAAW;QACJ,wCAAkB;GALrC,eAAe,CAyJ3B"}