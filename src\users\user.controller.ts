import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { UserService } from './user.service';

@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('create')
  async createUser(@Body() body: { telegram_id: string; username: string; firstname: string }) {
    if (!body.telegram_id || !body.username || !body.firstname) {
      throw new BadRequestException('Missing required fields');
    }
    return this.userService.createUser(body.telegram_id, body.username, body.firstname);
  }

  @Post('update-balance')
  async updateBalance(@Body() body: { telegram_id: string; amount: number }) {
    if (!body.telegram_id || body.amount === undefined) {
      throw new BadRequestException('Missing required fields');
    }
    const user = await this.userService.updateBalance(body.telegram_id, body.amount);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  @Post('deduct-balance')
  async deductBalance(@Body() body: { telegram_id: string; amount: number }) {
    if (!body.telegram_id || body.amount === undefined) {
      throw new BadRequestException('Missing required fields');
    }
    const success = await this.userService.deductBalance(body.telegram_id, body.amount);
    if (!success) {
      throw new BadRequestException('Insufficient balance or user not found');
    }
    return { success: true };
  }

  @Get('leaderboard')
  async getLeaderboard() {
    return this.userService.getLeaderboard();
  }

  @Get(':telegram_id')
  async getUser(@Param('telegram_id') telegram_id: string) {
    const user = await this.userService.getUserById(telegram_id);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return {
      telegram_id: user.telegram_id,
      username: user.username,
      firstname: user.firstname,
      balance_ton: user.balance_ton,
      ton_wallet_address: user.ton_wallet_address,
    };
  }

  @Post('ban')
  async banUser(@Body() body: { telegram_id: string }) {
    if (!body.telegram_id) {
      throw new BadRequestException('Missing telegram_id');
    }
    const success = await this.userService.banUser(body.telegram_id);
    if (!success) {
      throw new NotFoundException('User not found');
    }
    return { success: true };
  }

  @Post('unban')
  async unbanUser(@Body() body: { telegram_id: string }) {
    if (!body.telegram_id) {
      throw new BadRequestException('Missing telegram_id');
    }
    const success = await this.userService.unbanUser(body.telegram_id);
    if (!success) {
      throw new NotFoundException('User not found');
    }
    return { success: true };
  }

  @Post('update-wallet')
  async updateWallet(@Body() body: { telegram_id: string; wallet_address: string }) {
    if (!body.telegram_id || !body.wallet_address) {
      throw new BadRequestException('Missing required fields');
    }
    const user = await this.userService.updateWalletAddress(body.telegram_id, body.wallet_address);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }
}
