import { Repository } from 'typeorm';
import { Transaction, TransactionKind } from './transaction.entity';
export declare class TransactionService {
    private transactionRepository;
    constructor(transactionRepository: Repository<Transaction>);
    createTransaction(user_id: string, kind: TransactionKind, amount_ton: number, ref?: string, game_type?: string, game_data?: any): Promise<Transaction>;
    getUserTransactions(user_id: string, limit?: number): Promise<Transaction[]>;
    getAllTransactions(limit?: number): Promise<Transaction[]>;
    getTransactionsByKind(kind: TransactionKind, limit?: number): Promise<Transaction[]>;
    getTransactionsByGame(game_type: string, limit?: number): Promise<Transaction[]>;
    getUserGameStats(user_id: string): Promise<any>;
}
