{"version": 3, "sources": ["../../src/error/ReturningStatementNotSupportedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;;GAGG;AACH,MAAa,mCAAoC,SAAQ,2BAAY;IACjE;QACI,KAAK,CACD,0GAA0G,CAC7G,CAAA;IACL,CAAC;CACJ;AAND,kFAMC", "file": "ReturningStatementNotSupportedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to build a query with RETURNING / OUTPUT statement,\n * but used database does not support it.\n */\nexport class ReturningStatementNotSupportedError extends TypeORMError {\n    constructor() {\n        super(\n            `OUTPUT or RETURNING clause only supported by PostgreSQL, MariaDB, Microsoft SqlServer or Google Spanner.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}